apiVersion: v1
kind: ConfigMap
metadata:
  name: skynet-agent-config
  namespace: skynet-agent-system
data:
  agent.properties: |
    server.ip=*************
    server.port=32630
    agent.project-version=3.4.2
    agent.build-sid=1011
    zookeeper.server-list=************:2181
    zookeeper.cluster-name=skynet
    skynet.manager.url=http://************:2230
    skynet.home=/iflytek/server/skynet
    skynet.auth.api-key=skynet
    skynet.auth.api-secret=SKYNET_API_SECRET_PLACEHOLDER
    registry.url=*************:5000

package controller

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"iflytek.com/skynet/agent/skynetapp/client"
	skynetappv1alpha1 "iflytek.com/skynet/agent/skynetapp/v1alpha1"
	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/client-go/kubernetes/scheme"
)

func FetchWorkersState(c *gin.Context) {
	workers, err := GetWorkersState()
	if err != nil {
		c.JSON(http.StatusInternalServerError, err.Error())
		return
	}
	c.JSON(http.StatusOK, workers)
}

// 获取所有服务状态
func GetWorkersState() ([]WorkersState, error) {

	workers := []WorkersState{}

	// get skynetApp list
	skynetapps, err := client.GetSkynetAppList()
	if err != nil {
		fmt.Printf("Get SkynetApp List err %s\n", err.Error())
		return workers, err
	}

	// get deployment list
	deployments, err := client.GetDeploymentList()
	if err != nil {
		fmt.Printf("Get Deployment List err %s\n", err.Error())
		return workers, err
	}

	// get statefulset list
	statefulsets, err := client.GetStatefulSetList()
	if err != nil {
		fmt.Printf("Get StatefulSet List err %s\n", err.Error())
		return workers, err
	}

	// get daemonset list
	daemonsets, err := client.GetDaemonSetList()
	if err != nil {
		fmt.Printf("Get DaemonSet List err %s\n", err.Error())
		return workers, err
	}

	// convert to WorkersState
	for _, d := range skynetapps.Items {
		state, err := getActionState(&d, deployments, statefulsets, daemonsets)
		if err != nil {
			fmt.Printf("Get Action state error %s %s\n", d.Name, err.Error())
			continue
		}
		fmt.Printf("Get Action state %s %s\n", d.Name, state.Up)
		workers = append(workers, WorkersState{
			Index: int(d.Spec.Index),
			Aid:   getActionId(&d),
			Action: ActionDetail{
				Ip:           viper.GetString("server.ip"),
				Port:         int(d.Spec.BootOption.Ports[0].NodePort),
				AppPort:      getAppPort(&d),
				ExtPorts:     getExtPorts(&d),
				Pid:          0,
				Name:         d.Name,
				Up:           state.Up,
				WorkloadType: state.WorkloadType,
				Index:        int(d.Spec.Index),
				SubIndex:     0,
				Code:         d.Spec.ActionCode,
				Plugin:       d.Spec.PluginCode,
				Enable:       true,
				BootType:     d.Spec.BootType,
				FullName:     d.Spec.ActionCode + "@" + d.Spec.PluginCode,
				Aid:          getActionId(&d),
				Replicas:     state.Replicas,
			},
			State: *state,
		})
	}
	return workers, nil
}

// 获取服务的主端口
func getAppPort(skynetApp *skynetappv1alpha1.SkynetApp) int {
	appPort := 0
	commands := skynetApp.Spec.InitOption.Command
	for i, c := range commands {
		if c == "--port" && i < len(commands)-1 {
			portStr := commands[i+1]
			if portStr != "" {
				appPort, _ = strconv.Atoi(portStr)
			}
		}
	}
	return appPort
}

// 获取服务的扩展端口
func getExtPorts(skynetApp *skynetappv1alpha1.SkynetApp) []int {
	extPorts := []int{}
	commands := skynetApp.Spec.InitOption.Command
	for i, c := range commands {
		if c == "--ports" && i < len(commands)-1 {
			portStr := commands[i+1]
			if portStr != "" {
				ports := strings.Split(portStr, ",")
				for _, port := range ports {
					extPort, _ := strconv.Atoi(port)
					extPorts = append(extPorts, extPort)
				}
			}
		}
	}
	return extPorts
}

// 获取服务的唯一标识
func getActionId(skynetApp *skynetappv1alpha1.SkynetApp) string {
	if skynetApp.Spec.Index == 0 {
		return skynetApp.Spec.ActionCode + "@" + skynetApp.Spec.PluginCode
	}
	return skynetApp.Spec.ActionCode + "@" + skynetApp.Spec.PluginCode + "_" + strconv.Itoa(int(skynetApp.Spec.Index))
}

// 'UP': 'running',
// 'DOWN': 'failed',
// 'LOADING': 'starting'
func getActionState(skynetApp *skynetappv1alpha1.SkynetApp,
	deployments *appsv1.DeploymentList, statefulsets *appsv1.StatefulSetList, daemonsets *appsv1.DaemonSetList) (*StateDetail, error) {
	_, err := client.GetSkynetApp(skynetApp.Name)
	if err != nil {
		return nil, err
	}
	skynetAppStatus, err := client.GetSkynetAppStatus(skynetApp.Name)
	if err != nil {
		return nil, err
	}
	skynetAppType := getSkynetAppType(skynetApp)
	stateDetail := StateDetail{
		WorkloadType: skynetAppType,
		State:        parseSkynetAppStatus(skynetAppStatus),
		StartTime:    GetSkynetAppStartTime(skynetApp),
	}
	if skynetAppType == "statefulset" {
		stateDetail.Up, stateDetail.Replicas = queryStatefulSetState(skynetApp.Name, statefulsets, skynetAppStatus)
	} else if skynetAppType == "daemonset" {
		stateDetail.Up, stateDetail.Replicas = queryDaemonSetState(skynetApp.Name, daemonsets, skynetAppStatus)
	} else {
		stateDetail.Up, stateDetail.Replicas = queryDeploymentState(skynetApp.Name, deployments, skynetAppStatus)
	}
	return &stateDetail, nil
}

// 查询 deployment 状态
func queryDeploymentState(name string, deployments *appsv1.DeploymentList, skynetAppStatus *skynetappv1alpha1.SkynetAppStatus) (string, int32) {
	if isFail(skynetAppStatus) {
		return "DOWN", 0
	}
	deployment := getDeployment(name, deployments)
	if deployment == nil || deployment.Spec.Replicas == nil {
		return "DOWN", 0
	}
	return getDeploymentState(deployment), *(deployment.Spec.Replicas)
}

// 获取 deployment
func getDeployment(name string, deployments *appsv1.DeploymentList) *appsv1.Deployment {
	for _, d := range deployments.Items {
		if d.Name == name {
			return &d
		}
	}
	return nil
}

// 获取 deployment 状态
func getDeploymentState(deployment *appsv1.Deployment) string {
	if deployment == nil {
		return "LOADING"
	}
	if deployment.Status.AvailableReplicas == deployment.Status.ReadyReplicas && deployment.Status.AvailableReplicas == deployment.Status.Replicas {
		return "UP"
	} else {
		return "LOADING"
	}
}

// 查询 statefulset 状态
func queryStatefulSetState(name string, statefulsets *appsv1.StatefulSetList, skynetAppStatus *skynetappv1alpha1.SkynetAppStatus) (string, int32) {
	if isFail(skynetAppStatus) {
		return "DOWN", 0
	}
	statefulset := getStatefulSet(name, statefulsets)
	if statefulset == nil || statefulset.Spec.Replicas == nil {
		return "DOWN", 0
	}
	return getStatefulSetState(statefulset), *(statefulset.Spec.Replicas)
}

// 获取 statefulset
func getStatefulSet(name string, statefulsets *appsv1.StatefulSetList) *appsv1.StatefulSet {
	for _, d := range statefulsets.Items {
		if d.Name == name {
			return &d
		}
	}
	return nil
}

// 获取 statefulset 状态
func getStatefulSetState(statefulset *appsv1.StatefulSet) string {
	if statefulset == nil {
		return "LOADING"
	}
	if statefulset.Status.AvailableReplicas == statefulset.Status.ReadyReplicas && statefulset.Status.AvailableReplicas == statefulset.Status.Replicas {
		return "UP"
	} else {
		return "LOADING"
	}
}

// 查询 daemonset 状态
func queryDaemonSetState(name string, daemonsets *appsv1.DaemonSetList, skynetAppStatus *skynetappv1alpha1.SkynetAppStatus) (string, int32) {
	if isFail(skynetAppStatus) {
		return "DOWN", 0
	}
	daemonset := getDaemonSet(name, daemonsets)
	if daemonset == nil {
		return "DOWN", 0
	}
	return getDaemonSetState(daemonset), 1
}

// 获取 daemonset
func getDaemonSet(name string, daemonsets *appsv1.DaemonSetList) *appsv1.DaemonSet {
	for _, d := range daemonsets.Items {
		if d.Name == name {
			return &d
		}
	}
	return nil
}

// 获取 daemonset 状态
func getDaemonSetState(daemonset *appsv1.DaemonSet) string {
	if daemonset == nil {
		return "LOADING"
	}
	if daemonset.Status.CurrentNumberScheduled == daemonset.Status.DesiredNumberScheduled {
		return "UP"
	} else {
		return "LOADING"
	}
}

// 判断 SkynetApp 类型
func getSkynetAppType(skynetApp *skynetappv1alpha1.SkynetApp) string {
	if skynetApp.Spec.BootType == "K8sBoot" {
		yaml := skynetApp.Spec.BootOption.Yaml
		blocks := strings.Split(yaml, "---")
		for _, block := range blocks {
			decode := scheme.Codecs.UniversalDeserializer().Decode
			obj, _, err := decode([]byte(block), nil, nil)
			if err != nil {
				fmt.Printf("Error while decoding YAML object [%s] error %s\n", skynetApp.Name, err)
				continue
			}
			switch obj.(type) {
			case *appsv1.Deployment:
				return "deployment"
			case *appsv1.StatefulSet:
				return "statefulset"
			case *appsv1.DaemonSet:
				return "daemonset"
			}
		}
	}
	return "deployment"
}

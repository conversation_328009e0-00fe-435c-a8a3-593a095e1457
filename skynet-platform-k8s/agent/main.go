package main

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	log "github.com/cihub/seelog"
	"github.com/gin-gonic/gin"
	"github.com/go-zookeeper/zk"
	"github.com/spf13/viper"
	"github.com/tidwall/gjson"
	"iflytek.com/skynet/agent/controller"
	"iflytek.com/skynet/agent/reporter"
	"iflytek.com/skynet/agent/skynetapp/builder"
	"iflytek.com/skynet/agent/skynetapp/client"
	skynetappv1alpha1 "iflytek.com/skynet/agent/skynetapp/v1alpha1"
)

func init() {
	config := "seelog.xml"
	log.Info("InitLog ", config)

	logger, err := log.LoggerFromConfigAsFile(config)
	if err != nil {
		panic(err)
	}
	log.ReplaceLogger(logger)
}

// init config from agent.properties file
func initConfig() {
	viper.SetConfigName("agent")
	viper.SetConfigType("properties")
	viper.AddConfigPath(".")
	err := viper.ReadInConfig()
	if err != nil {
		panic(fmt.Errorf("fatal error config file: %w", err))
	}
}

// setup request mappings
func setupRequestMappings() *gin.Engine {
	r := gin.Default()
	r.GET("/", controller.Home)
	r.GET("/properties", controller.Properties)
	r.GET("/skynet/agent/ctrl/FETCH_SERVER_STATE", controller.FetchServerStatus)
	r.GET("/skynet/agent/ctrl/FETCH_WORKERS_STATE", controller.FetchWorkersState)
	r.POST("/skynet/agent/ctrl/REBOOT_WORKER", controller.RebootWorker)
	r.GET("/skynet/agent/node-status/:aid", controller.FetchNodeStatus)
	r.GET("/skynet/agent/boot-status/:aid", controller.FetchBootStatus)
	r.GET("/skynet/agent/node-status", controller.FetchAgentStatus)
	r.GET("/skynet/agent/log/ws/:aid", controller.LogHandler)
	return r
}

func getEnabledActionList(conn *zk.Conn, zkPath string) ([]controller.ActionInfo, error) {
	topology, _, err := conn.Get(zkPath)
	if err != nil {
		return nil, err
	}
	topologyInfo := controller.TopologyInfo{}
	if err := json.Unmarshal(topology, &topologyInfo); err != nil {
		return nil, err
	}

	actionList := []controller.ActionInfo{}
	for _, action := range topologyInfo.Actions {
		if !action.Enable {
			continue
		}
		for i := 0; i < action.Num; i++ {
			actionList = append(actionList, controller.ActionInfo{
				Index:        i,
				Code:         action.Code,
				Name:         action.Name,
				Num:          action.Num,
				Replicas:     action.Replicas,
				NodeSelector: action.NodeSelector,
				Plugin:       action.Plugin,
				Enable:       action.Enable,
			})
		}
	}
	return actionList, nil
}

func getSkynetAppName(action controller.ActionInfo) string {
	name := action.Code + "-" + action.Plugin
	if action.Index > 0 {
		name = action.Code + "-" + action.Plugin + "-" + strconv.Itoa(action.Index)
	}
	return name
}

// Find the skynetApp matched the action info
func findSkynetApp(action controller.ActionInfo, skynetAppList *skynetappv1alpha1.SkynetAppList) *skynetappv1alpha1.SkynetApp {
	skynetAppName := getSkynetAppName(action)
	for _, skynetApp := range skynetAppList.Items {
		if skynetApp.Name == skynetAppName {
			return &skynetApp
		}
	}
	return nil
}

// Check if the skynetApp is not in the actionList, we need remove it.
func isSkynetAppMissing(skynetApp skynetappv1alpha1.SkynetApp, actionList []controller.ActionInfo) bool {
	for _, action := range actionList {
		if getSkynetAppName(action) == skynetApp.Name {
			return false
		}
	}
	return true
}

// read `/skynet/plugin/<plugin>/action/<action>` to get the action ports
func getActionPorts(conn *zk.Conn, action controller.ActionInfo) []int32 {

	basePath := fmt.Sprintf("/%s/plugin/%s/action/%s",
		viper.GetString("zookeeper.cluster-name"), action.Plugin, action.Code)
	xbootParamPath := fmt.Sprintf("%s/_xboot_param", basePath)
	xbootParam, _, err := conn.Get(xbootParamPath)
	if err != nil {
		panic(err)
	}
	results := []int32{}
	ports := gjson.Get(string(xbootParam), "ports")
	if ports.Exists() {
		for _, port := range strings.Split(ports.String(), ",") {
			intPort, _ := strconv.Atoi(port)
			results = append(results, int32(intPort))
		}
	}
	return results
}

// read `/skynet/plugin/<plugin>/action/<action>` to get the action port
func getActionPort(conn *zk.Conn, action controller.ActionInfo) int32 {

	basePath := fmt.Sprintf("/%s/plugin/%s/action/%s",
		viper.GetString("zookeeper.cluster-name"), action.Plugin, action.Code)
	portPath := fmt.Sprintf("%s/_port", basePath)
	port, _, err := conn.Get(portPath)
	if err != nil {
		panic(err)
	}
	intPort, _ := strconv.Atoi(string(port))
	return int32(intPort)
}

// read `/skynet/plugin/<plugin>/action/<action>` to get the action boot type
func getActionBootType(conn *zk.Conn, action controller.ActionInfo) string {

	basePath := fmt.Sprintf("/%s/plugin/%s/action/%s",
		viper.GetString("zookeeper.cluster-name"), action.Plugin, action.Code)

	xbootTypePath := fmt.Sprintf("%s/_xboot_type", basePath)
	xbootType, _, err := conn.Get(xbootTypePath)
	if err != nil {
		panic(err)
	}
	return string(xbootType)
}

// Create a SkynetApps by the action info
func createSkynetApp(conn *zk.Conn, action controller.ActionInfo) {

	bootType := getActionBootType(conn, action)
	port := getActionPort(conn, action)
	if port != 0 {
		port = port + int32(action.Index)
	}
	ports := getActionPorts(conn, action)
	ports = append([]int32{port}, ports...)
	skynetApp := builder.NewBuilder(bootType, ports, action).BuildSkynetApp()
	fmt.Printf("Creating SkynetApp [%s] ...\n", skynetApp.Name)
	_, err := client.CreateSkynetApp(&skynetApp)
	if err != nil {
		fmt.Printf("Create SkynetApp [%s] err %s\n", skynetApp.Name, err.Error())
		jsonData, err := json.Marshal(skynetApp)
		if err != nil {
			fmt.Printf("SkynetApp = [%s]\n", string(jsonData))
		}
	} else {
		fmt.Printf("Create SkynetApp [%s] success\n", skynetApp.Name)
		reporter.ReportActionStatusImmediately(conn)
	}
}

// Update the SkynetApps by the action info
func updateSkynetApp(conn *zk.Conn, action controller.ActionInfo, skynetApp *skynetappv1alpha1.SkynetApp) {

	fmt.Printf("Updating SkynetApp [%s] ...\n", skynetApp.Name)
	replicas := int32(action.Replicas)
	skynetApp.Spec.NodeSelector = action.NodeSelector
	skynetApp.Spec.Replicas = &replicas
	_, err := client.UpdateSkynetApp(skynetApp)
	if err != nil {
		fmt.Printf("Update SkynetApp [%s] err: %s\n", skynetApp.Name, err.Error())
	} else {
		fmt.Printf("Update SkynetApp [%s] success\n", skynetApp.Name)
		reporter.ReportActionStatusImmediately(conn)
	}
}

// Delete the SkynetApp
func deleteSkynetApp(conn *zk.Conn, skynetApp skynetappv1alpha1.SkynetApp) {
	fmt.Printf("Deleting SkynetApp [%s] ...\n", skynetApp.Name)
	_, err := client.DeleteSkynetApp(skynetApp)
	if err != nil {
		fmt.Printf("Delete SkynetApp [%s] err: %s\n", skynetApp.Name, err.Error())
	} else {
		fmt.Printf("Delete SkynetApp [%s] success\n", skynetApp.Name)
		reporter.ReportActionStatusImmediately(conn)
	}
}

func checkSkynetApp(conn *zk.Conn, zkPath string) {

	// get skynetApp list
	skynetAppList, err := client.GetSkynetAppList()
	if err != nil {
		fmt.Printf("Get SkynetApp List err %s\n", err.Error())
		return
	}

	// get enabled action list
	actionList, err := getEnabledActionList(conn, zkPath)
	if err != nil {
		fmt.Printf("Get Enabled ActionList err %s\n", err.Error())
		return
	}

	// action exists, but skynetapp not exists, then create skynetapp by action info
	for _, action := range actionList {
		skynetApp := findSkynetApp(action, skynetAppList)
		if skynetApp == nil {
			// skynetApp not exists
			createSkynetApp(conn, action)
		} else if isChanged(action, skynetApp) {
			// skynetApp exists, but changed
			updateSkynetApp(conn, action, skynetApp)
		} else {
			// skynetApp not changed
			fmt.Printf("SkynetApp [%s] not changed, nodeSelector = %s, replicas = %d\n", skynetApp.Name, action.NodeSelector, action.Replicas)
		}
	}

	// skynetapp exists, but action not exists, then delete the skynetapp
	for _, skynetApp := range skynetAppList.Items {
		if isSkynetAppMissing(skynetApp, actionList) {
			deleteSkynetApp(conn, skynetApp)
		}
	}
}

// 判断 skynetApp 是否需要更新
func isChanged(action controller.ActionInfo, skynetApp *skynetappv1alpha1.SkynetApp) bool {
	ns1 := action.NodeSelector
	if ns1 == nil {
		ns1 = map[string]string{}
	}
	ns2 := skynetApp.Spec.NodeSelector
	if ns2 == nil {
		ns2 = map[string]string{}
	}
	return !reflect.DeepEqual(ns1, ns2) || action.Replicas != int(*skynetApp.Spec.Replicas)
}

// callback when the zk node change event occurs
func callback(conn *zk.Conn, e zk.Event) {
	fmt.Println("++++++++++++++++++++++++")
	fmt.Println("path:", e.Path)
	fmt.Println("type:", e.Type.String())
	fmt.Println("state:", e.State.String())
	fmt.Println("------------------------")

	if e.State.String() == "StateDisconnected" {
		panic("zk is disconnected, exiting!")
	}

	checkSkynetApp(conn, e.Path)
}

// watch a zk path
func watchPath(conn *zk.Conn, zkPath string) {
	exists, _, eventChannel, err := conn.ExistsW(zkPath)
	if err != nil {
		panic(err)
	}
	if !exists {
		panic("ZK path not exist.")
	}
	go func() {
		e := <-eventChannel
		callback(conn, e)
		watchPath(conn, zkPath)
	}()
}

// connect to zookeeper
func initZookeeper() *zk.Conn {
	serverList := viper.GetString("zookeeper.server-list")
	conn, _, err := zk.Connect(strings.Split(serverList, ","), time.Second)
	if err != nil {
		panic(err)
	}
	return conn
}

// watch the topology path
func watchTopology(conn *zk.Conn) {
	zkPath := fmt.Sprintf("/%s/cluster/topology/%s",
		viper.GetString("zookeeper.cluster-name"), viper.GetString("server.ip"))
	watchPath(conn, zkPath)
	checkSkynetApp(conn, zkPath)
}

// 将 Agent 和 Action 状态汇报到 Zookeeper
func reportStatus(conn *zk.Conn) {
	go func() {
		reporter.ReportAgentStatus(conn)
		reporter.ReportActionStatus(conn)
	}()
}

// entry point
func main() {

	// read config from agent.properties file
	initConfig()

	// watch zookeeper
	conn := initZookeeper()
	defer conn.Close()

	watchTopology(conn)
	reportStatus(conn)

	// start the web server
	r := setupRequestMappings()
	r.Run(":" + viper.GetString("server.port"))
}

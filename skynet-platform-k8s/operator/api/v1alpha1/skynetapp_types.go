/*
Copyright 2022.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

type PortOption struct {
	ContainerPort int32 `json:"containerPort"`
	NodePort      int32 `json:"nodePort"`
}

type EnvOption struct {
	Name  string `json:"name"`
	Value string `json:"value"`
}

type MountOption struct {
	Name      string `json:"name"`
	MountPath string `json:"mountPath"`

	// emptyDir or hostPath
	MountType        string `json:"mountType"`
	MountPropagation string `json:"mountPropagation,omitempty"`

	// when mountType == "emptyDir"
	SubPath string `json:"subPath,omitempty"`

	// when mountType == "hostPath"
	Path     string `json:"path,omitempty"`
	PathType string `json:"pathType,omitempty"`
}

type InitOption struct {
	Image   string   `json:"image"`
	Command []string `json:"command,omitempty"`
}

type BootOption struct {
	Image       string        `json:"image,omitempty"`
	Command     []string      `json:"command,omitempty"`
	Args        []string      `json:"args,omitempty"`
	Ports       []PortOption  `json:"ports,omitempty"`
	Envs        []EnvOption   `json:"envs,omitempty"`
	Mounts      []MountOption `json:"mounts,omitempty"`
	Privileged  *bool         `json:"privileged,omitempty"`
	HostNetwork bool          `json:"hostNetwork,omitempty"`
	Gpus        int           `json:"gpus,omitempty"`
	Yaml        string        `json:"yaml,omitempty"`
}

// SkynetAppSpec defines the desired state of SkynetApp
type SkynetAppSpec struct {
	// INSERT ADDITIONAL SPEC FIELDS - desired state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	ActionCode   string            `json:"actionCode"`
	PluginCode   string            `json:"pluginCode"`
	Index        int32             `json:"index"`
	Replicas     *int32            `json:"replicas"`
	BootType     string            `json:"bootType"`
	SkynetHome   string            `json:"skynetHome"`
	InitOption   InitOption        `json:"initOption"`
	BootOption   BootOption        `json:"bootOption"`
	MeshOption   *BootOption       `json:"meshOption"`
	NodeSelector map[string]string `json:"nodeSelector,omitempty"`
	Props        map[string]string `json:"props,omitempty"`
}

type SkynetAppResourceStatus struct {
	Type    string `json:"type,omitempty"`
	Name    string `json:"name,omitempty"`
	Status  string `json:"status,omitempty"`
	Message string `json:"message,omitempty"`
}

// SkynetAppStatus defines the observed state of SkynetApp
type SkynetAppStatus struct {
	// INSERT ADDITIONAL STATUS FIELD - define observed state of cluster
	// Important: Run "make" to regenerate code after modifying this file

	Resources []SkynetAppResourceStatus `json:"resources,omitempty"`
}

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status

// SkynetApp is the Schema for the skynetapps API
type SkynetApp struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   SkynetAppSpec   `json:"spec,omitempty"`
	Status SkynetAppStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// SkynetAppList contains a list of SkynetApp
type SkynetAppList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []SkynetApp `json:"items"`
}

func init() {
	SchemeBuilder.Register(&SkynetApp{}, &SkynetAppList{})
}

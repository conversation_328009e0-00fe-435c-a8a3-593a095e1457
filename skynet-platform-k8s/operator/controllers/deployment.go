package controllers

import (
	"context"
	"fmt"

	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"

	skynetv1alpha1 "iflytek.com/skynet/operator/api/v1alpha1"
)

// 创建 or 更新 deployment
func (r *SkynetAppReconciler) updateDeployment(skynetApp *skynetv1alpha1.SkynetApp, deploymentToUpdate *appsv1.Deployment) error {

	deploymentName := types.NamespacedName{
		Namespace: deploymentToUpdate.Namespace,
		Name:      deploymentToUpdate.Name,
	}

	// 查询 deployment 是否存在
	deployment := &appsv1.Deployment{}
	fmt.Printf("Get deployment [%s] for SkynetApp [%s] ...\n", deploymentToUpdate.Name, skynetApp.Name)
	if err := r.Get(context.TODO(), deploymentName, deployment); err != nil {
		if errors.IsNotFound(err) {
			// deployment 不存在，则创建一个新的
			fmt.Printf("Get deployment [%s] for SkynetApp [%s] not found\n", deploymentToUpdate.Name, skynetApp.Name)
			fmt.Printf("Create deployment [%s] for SkynetApp [%s] ...\n", deploymentToUpdate.Name, skynetApp.Name)
			ctrl.SetControllerReference(skynetApp, deploymentToUpdate, r.Scheme)
			if err := r.Create(context.TODO(), deploymentToUpdate); err != nil {
				fmt.Printf("Create deployment [%s] for SkynetApp [%s] error\n", deploymentToUpdate.Name, skynetApp.Name)
				return err
			}
			return nil
		} else {
			fmt.Printf("Get deployment [%s] for SkynetApp [%s] error\n", deploymentToUpdate.Name, skynetApp.Name)
			return err
		}
	} else {
		// deployment 已存在，则更新
		fmt.Printf("Get deployment [%s] for SkynetApp [%s] success\n", deploymentToUpdate.Name, skynetApp.Name)
		deployment.Spec = deploymentToUpdate.Spec
		ctrl.SetControllerReference(skynetApp, deployment, r.Scheme)
		fmt.Printf("Update deployment [%s] for SkynetApp [%s] ...\n", deploymentToUpdate.Name, skynetApp.Name)
		if err := r.Update(context.TODO(), deployment); err != nil {
			fmt.Printf("Update deployment [%s] for SkynetApp [%s] error\n", deploymentToUpdate.Name, skynetApp.Name)
			return err
		}
	}
	return nil
}

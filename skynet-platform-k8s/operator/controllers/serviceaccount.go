package controllers

import (
	"context"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"

	skynetv1alpha1 "iflytek.com/skynet/operator/api/v1alpha1"
)

// 创建 or 更新 serviceAccount
func (r *SkynetAppReconciler) updateServiceAccount(skynetApp *skynetv1alpha1.SkynetApp, serviceAccountToUpdate *corev1.ServiceAccount) error {

	serviceAccountName := types.NamespacedName{
		Namespace: serviceAccountToUpdate.Namespace,
		Name:      serviceAccountToUpdate.Name,
	}

	// 查询 serviceAccount 是否存在
	fmt.Printf("Get serviceAccount [%s] for SkynetApp [%s] ...\n", serviceAccountToUpdate.Name, skynetApp.Name)
	serviceAccount := &corev1.ServiceAccount{}
	if err := r.Get(context.TODO(), serviceAccountName, serviceAccount); err != nil {
		if errors.IsNotFound(err) {
			// serviceAccount 不存在，则创建一个新的
			fmt.Printf("Get serviceAccount [%s] for SkynetApp [%s] not found\n", serviceAccountToUpdate.Name, skynetApp.Name)
			fmt.Printf("Create serviceAccount [%s] for SkynetApp [%s] ...\n", serviceAccountToUpdate.Name, skynetApp.Name)
			ctrl.SetControllerReference(skynetApp, serviceAccountToUpdate, r.Scheme)
			if err := r.Create(context.TODO(), serviceAccountToUpdate); err != nil {
				fmt.Printf("Create serviceAccount [%s] for SkynetApp [%s] error\n", serviceAccountToUpdate.Name, skynetApp.Name)
				return err
			}
			return nil
		} else {
			// 获取 serviceAccount 出错
			fmt.Printf("Get serviceAccount [%s] for SkynetApp [%s] error\n", serviceAccountToUpdate.Name, skynetApp.Name)
			return err
		}
	} else {
		// serviceAccount 无需更新
		fmt.Printf("Get serviceAccount [%s] for SkynetApp [%s] success\n", serviceAccountToUpdate.Name, skynetApp.Name)
	}
	return nil
}

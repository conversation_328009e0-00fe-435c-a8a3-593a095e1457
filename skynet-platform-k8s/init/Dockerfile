# Build the init binary
FROM golang:1.18 as builder

ENV GOPROXY=https://goproxy.cn,direct
WORKDIR /workspace
# Copy the Go Modules manifests
COPY go.mod go.mod
COPY go.sum go.sum
# cache deps before building and copying source so that we don't need to re-download as much
# and so that source changes don't invalidate our downloaded layer
RUN go mod download

RUN mkdir -p log resources

# Copy the go source
COPY main.go main.go
COPY init.properties init.properties
COPY seelog.xml seelog.xml
COPY hub_client/ hub_client/
COPY skynet_client/ skynet_client/
COPY version/ version/

# Build
ARG GOOS=linux
ARG GOARCH=amd64
RUN CGO_ENABLED=0 GOOS=${GOOS} GOARCH=${GOARCH} go build -a -o init main.go

FROM alpine:3.17
WORKDIR /
COPY --from=builder /workspace/init .
COPY --from=builder /workspace/init.properties .
COPY --from=builder /workspace/seelog.xml .
COPY --from=builder --chown=65532:65532 /workspace/log log
COPY --from=builder --chown=65532:65532 /workspace/resources resources
USER 65532:65532

ENTRYPOINT ["/init"]

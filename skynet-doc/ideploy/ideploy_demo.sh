#!/bin/bash

set -e
export PYTHONIOENCODING=utf-8

# 下载执行脚本, IPACKAGE_USER 和 IPACKAGE_TOKEN 通过 iDeploy/项目配置/项目自定义参数配置，不要在脚本内写死
#IPACKAGE_USER='leitong'
#IPACKAGE_TOKEN='AKCp8nzBxK7phWdkwd1hZjuVjKzS1Fz3HaEHcd9TgUMsXtcbip3RDdaVsDTrMTWkXdraqETLP'
echo "IPACKAGE_USER: ${IPACKAGE_USER}"
echo "IPACKAGE_TOKEN: ${IPACKAGE_TOKEN}"
curl -s -u $IPACKAGE_USER:$IPACKAGE_TOKEN -o "ideploy.py" http://console.devops.iflytek.com/restapi/ipackage/v1/download/artifact?fullPath=%2FHY-private-repo%2F%E4%BA%91%E8%AE%A1%E7%AE%97%E7%A0%94%E7%A9%B6%E9%99%A2%2FTURING%2FPlatform%2FAI_management%2Fskynet%2Fideploy-scripts%2Fideploy.py

# 脚本环境变量配置：一个skynet服务对应一个替换文件(变量名需满足模式'ACTION_FILE_(.+)'）
# 冒号前代表服务编码，冒号后代表文件名的匹配字符串，默认的匹配模式为'字符串包含'，如果USE_REGEX=true，匹配模式为正则表达式匹配
#export PLUGIN_NAME='flint-bml'
#export ACTION_FILE_1='flint-bml-eval:flint-bml-eval'
#export ACTION_FILE_2='flint-bml-train:flint-bml-train'
#export ACTION_FILE_3='flint-data-management:flint-data-management'
#export ACTION_FILE_4='flint-mark-contract:flint-mark-contract'

# (PLUGIN_NAME不设置时必须使用完整编码) 上面代码块等同于:
# export ACTION_FILE_1='flint-bml-eval@flint-bml:flint-bml-eval'
# export ACTION_FILE_2='flint-bml-train@flint-bml:flint-bml-train'
# export ACTION_FILE_3='flint-data-management@flint-bml:flint-data-management'
# export ACTION_FILE_4='flint-mark-contract@flint-bml:flint-mark-contract'

# (服务编码和匹配表达式相同时，可使用'ACTION_(.+)'变量，省略冒号后面的内容) 上面代码块等同于:
# export PLUGIN_NAME='flint-bml'
# export ACTION_1='flint-bml-eval'
# export ACTION_2='flint-bml-train'
# export ACTION_3='flint-data-management'
# export ACTION_4='flint-mark-contract'

# 脚本环境变量配置：一个服务对应多个替换文件, 变量名需满足'FILE_(.+)_(\d+)'，注意必须以数字结尾
# 例子中的 'NGINX'可替换成任意字符串或者数字
#export ACTION_NGINX='turing-cloud-nginx-v120@ant-paas'
#export FILE_NGINX_1='skybox-web'
#export FILE_NGINX_2='skybox-flint-bml'
#export FILE_NGINX_3='skybox-flint'

# 脚本环境变量配置：skynet配置
#export SKYNET_HOST=http://************:2230
#export SKYNET_USERNAME='admin'
#export SKYNET_PWD='Skynet@2230'

# 脚本环境变量配置: (功能选项)是否重启服务
#export REBOOT='true'

# 脚本环境变量配置: (功能选项)是否使用正则表达式以匹配文件
#export USE_REGEX='false'

# 脚本环境变量配置: (功能选项)是否清理过期文件
#export CLEAR='true'

# 检测命令，执行脚本
if command -v python3 &> /dev/null; then
    cmd='python3'
elif command -v python2 &> /dev/null; then
    cmd='python2'
elif command -v python &> /dev/null; then
    cmd='python'
else
    echo "未找到合适的Python版本"
    exit 1
fi

${cmd} ./ideploy.py
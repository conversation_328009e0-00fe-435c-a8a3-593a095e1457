# skynet-3.4.x 常见问题FAQ
 
---

## 1、如何修改 xmanger 会话过期时间？

修改 `$SKYNET_HOME/conf/application.properties` 中的 `form-auth` 相关的配置，然后重启 xmanager 即可：

```properties
# manager session 过期时间
skynet.security.form-auth.jwt-expires-second=600
# manger登录 失败锁定时间
skynet.security.form-auth.fail-lock-duration-second=180
# manger登录 失败重试次数
skynet.security.form-auth.fail-tries-times=5
```

**特别注意**：如果上面的配置修改不生效，则登录 ZKUI，检查 `/skynet/xmanager/setting` 路径下是否存在相关配置：

![](./asserts/skynet-jwt-expires-second-setting.png)

如果存在，直接修改它的值，重启 xmanager 即可。

---

## 2、如何关闭 Web-shell？

修改 `$SKYNET_HOME/conf/application.properties` 中的 `webshell` 相关的配置，然后重启 xmanager 即可：

```properties
skynet.webshell.enabled=false
skynet.webshell.sftp-enabled=false
``` 

---

## 3、使用非本机IP(映射的)启动`manager`和`agent`服务，如何绕过IP检查？

修改 `$SKYNET_HOME/conf/application.properties` 中的 `skynet.check-ip.enabled` 配置，然后重启 xmanager 即可：

```properties
# Check the IP address is local IP ，default=true
skynet.check-ip.enabled=false
```

> 备注：如果是图聆的 AI 能力服务，也需要绕过 IP 检查，请在对应的能力服务的 `服务级别属性` 也添加 `skynet.check-ip.enabled=false` 配置，然后重启对应的服务。

---

## 4、托管的服务无法正常启动

答：常见的可能原因有：

### 4.1 系统中没有 `ps` 或 `python` 命令

可以直接在命令行上执行 `ps` 或 `python` 进行确认，如果系统中只安装了 `python3`，可以创建一个 `python` 的软链接指向 `python3`：

```
$ ln -s /path/to/python3 /usr/bin/python
```

### 4.2 端口号被占用

检查服务的 “启动详情”，是否有 **启动失败:The port xx is being used.** 这样的失败原因。一般有两种情况会导致这个问题：一种情况是服务定义中写死了端口号，如果在同一台服务器上部署多个实例，只有一个实例能部署成功，其他的实例会报端口被占用，可以将服务定义中的端口号改为 0，让 Skynet 随机分配；另一种情况是该端口号在服务器上确实已被占用，可以使用 netstat 或 lsof 等命令找到占用该端口的程序，kill 掉即可。

### 4.3 服务不是前台模式启动

检查服务的 “启动详情”，是否有 **The cmd run error or not foreground run mode. please try run xx on terminal.** 这样的失败原因。Skynet 上托管的服务必须以前台模式启动，可以将 “启动详情” 中的启动命令复制到服务器上手工执行下，如果执行后程序直接退出，说明不是以前台模式启动的，需要修改启动脚本。

### 4.4 `/dev/shm` 目录权限问题

如果出现一台服务器上的所有服务都无法启动，服务的 “启动详情” 失败原因和上一条一样，显示为 **The cmd run error or not foreground run mode. please try run xx on terminal.**，可以在服务启动时进入到 `/dev/shm/.skynet/action/aaa@bbb/` 目录，如果不存在 `target.sh` 脚本文件，可能是 `/dev/.shm` 目录权限问题，可以修改 Skynet 配置文件 `$SKYNET_HOME/conf/application.properties` 中的 `skynet.dev.shm.path` 配置。

> 注意，如果是老版本 Skynet，`skynet.dev.shm.path` 配置位于 `xagent.sh` 启动脚本中，配置文件修改无效。

### 4.5 可能是 v3.4.4 之前的版本遗留问题

如果你的 Skynet 是 v3.4.4 之前的老版本，而且使用了上面的方法仍然无法解决，可以点击进入节点详情页面，查看服务日志，观察服务启动时是否有 **app is running** 这样的提示信息，如果有，可能是该版本的遗留问题导致，推荐将 Skynet 升级到新版本，或者按照下面的步骤删除 `/dev/shm` 目录下的 `stream.pid` 和 `target.pid` 文件：

```
$ cd /dev/shm/.skynet/action/aaa@bbb
$ rm -rf stream.pid target.pid
```

然后再重新启动服务。

### 4.6 非 root 账号启动问题

如果 Skynet 是以非 root 账号启动，可能是该账号访问 Python 的权限不足导致，需要对该账号赋权，如果有 DockerBoot 服务，还需要对 docker.sock 文件赋权：

```
$ chown -R skynetuser: /usr/bin/python /var/run/docker.sock
```

其中 `skynetuser` 修改为 Skynet 的启动账号。

### 4.7 托管服务一直显示黄色（启动中状态）

检查该节点的服务日志（也就是 Agent 日志），发现有 *无法分配内存* 的现象，类似于下面这样：

```
02-19 14:19:10.785 -ERROR 19767[ant.health.t-1] s.p.c.utils.cmd.CommandLineExecutor     [242]: Exec Shell=[ps -e -o pid,ppid|grep 31396] command Error=Cannot run program "/bin/sh": error=12, 无法分配内存
java.io.IOException: Cannot run program "/bin/sh": error=12, 无法分配内存
	at java.lang.ProcessBuilder.start(ProcessBuilder.java:1048)
	at java.lang.Runtime.exec(Runtime.java:621)
	at java.lang.Runtime.exec(Runtime.java:486)
	at skynet.platform.common.utils.cmd.CommandLineExecutor.executeShell(CommandLineExecutor.java:217)
```

打开 `$SKYNET_HOME/bin/skynet.sh` 文件，找到 `JAVA_OPTS="-Xms512M -Xmx1G"` 这一行，将内存值调大，并重启 xagent 即可。

> 如果是老版本 Skynet，该配置位于 `$SKYNET_HOME/bin/ant-xagent.sh` 文件中。

### 4.8 `start.sh` 脚本执行超时

检查节点的服务日志，发现有 `The current command is relatively time-consuming.[chmod -R +x <...>]` 这样的报错：

![](./asserts/chmod-sonsuming.png)

这种情况一般是因为工作目录下包含了太多的文件，导致 `chmod` 耗时比较长，可以将该 `chmod` 命令在服务器上运行下，看看是不是超过了 5 秒。

解决方法有两种，第一种是对工作目录下的文件进行清理，将无关文件删除或移出工作目录；第二种是调整下面的 Skynet 参数，将超时时间改长点（注意是 v3.4.14 以上版本）：

```
skynet.shell.timeout=5000
```

### 4.9 `repofile` 文件损坏导致服务无法启动

Skynet 在启动服务时立即失败，检查节点的服务日志，发现类似下面的异常堆栈：

![](./asserts/repofile-error.png)

其中关键点位于调用 `LocalFileUtils.getUpdateParamList()` 函数时异常，可以确定是 `repofile` 文件损坏。进入 `$SKYNET_HOME/tmp` 目录，删除 `<EMAIL>` 文件，再重启服务即可。

---

## 5、`DockerBoot` 类型服务无法启动？

答：常见的可能原因有：

### 5.1 未安装 Docker 环境

通过执行 `docker --version` 命令，判断是否安装了 Docker 环境；如果没有，可以在注册服务器节点时，勾选 `注册时自动分发` -> `自动安装docker` 选项，则会自动安装；也可以通过 Skynet 部署包自带的安装脚本进行安装，进入 `SKYNET_HOME` 目录，手工执行 `docker-install.sh` 脚本即可：

```sh
$ cd /iflytek/server/skynet
$ ./runtime/docker-install.sh
```

### 5.2 检查 `docker.service` 是否带 `unix:///var/run/docker.sock` 启动参数？

Skynet 通过 `docker.sock` 套接字文件来和 Docker 进行通信，所以 Docker 启动时必须监听该套接字，打开 `/usr/lib/systemd/system/docker.service` 文件，将启动参数修改为类似下面这样：

```ini
ExecStart=/usr/bin/dockerd -H unix:///var/run/docker.sock
```

修改之后，重启 Docker 服务：

```
$ systemctl daemon-reload
$ systemctl restart docker
```

然后重启该机器上 Skynet Agent 服务即可。

如果机器上已经安装有 Docker 环境，可能 `docker.sock` 套接字文件的位置和上面不一样，可以通过下面的 Skynet 配置来修改：

```
skynet.docker.api.url=unix:///var/run/docker.sock
```

注意，针对不同的操作系统，`docker.service` 文件的位置可能不一样，如果 `/usr/lib/systemd/system/docker.service` 文件不存在，可以通过 `systemctl status docker` 命令进行确认：

```
$ systemctl status docker
● docker.service - Docker Application Container Engine
   Loaded: loaded (/etc/systemd/system/docker.service; enabled; vendor preset: disabled)
   Active: active (running) since Thu 2023-10-26 17:26:55 CST; 2h 38min ago
```

### 5.3 Skynet Agent 服务问题

查看 agent 日志，报 `The docker runtime is not install`，但是 Docker 明明已经安装了，而且 `docker.sock` 配置也没有问题，可能是 agent 服务本身的问题，可尝试重启 agent，再重启相关所有服务。

### 5.4 Docker 环境问题

如果以上方法都尝试过，但是 `DockerBoot` 类型的服务仍然无法启动，查看日志有类似下面这样的错误：

```
Error response from daemon: dial unix /run/containerd/s/xxx: connect: connection refused: unknown.
```

登录服务器，尝试用 `docker run` 启动容器报同样的错误，这个错可能是由于 containerd 服务导致的，可以将其卸载之：

```
$ systemctl stop containerd
$ rm -rf /usr/lib/systemd/system/containerd.service
$ systemctl daemon-reload
```

然后使用上面的方法重新安装 Docker 即可。

---

## 6、服务节点无法注册

答：常见的可能原因有：

### 6.1 SSH 版本不兼容

检查控制台日志，报错信息如下：

```
connect server error.Algorithm negotiation fail: algorithmName="server_host_key" jschProposal="ssh-ed25519,ecdsa-sha2-nistp256,ecdsa-sha2-nistp384,ecdsa-sha2-nistp521,rsa-sha2-512,rsa-sha2-256" serverProposal="ssh-rsa,ssh-dss"
```

如果你使用的是 v3.4.x 以上的 Skynet 版本，可能是因为服务器上的 SSH 版本较低不兼容，需要升级服务器上的 SSH 到 v7.4 以上的版本。由于 Skynet 使用了 [Jsch](https://github.com/mwiede/jsch) 来实现 SSH 的连接，最新版本的 Jsch 已经不支持 ssh-rsa、ssh-dss 等不安全的签名方案，如果你的 SSH 版本较老，就会出现连接不上的情况。

如果你使用的是 v3.4.x 以下的 Skynet 版本，可能是因为服务器上的 SSH 版本较高不兼容，需要升级 Skynet 到 v3.4 以上的版本。

### 6.2 未配置 `/etc/hosts` 文件

检查服务器的 `/etc/hosts`，是否存在错误的配置项，如果没有，则尝试将服务器的 IP 和主机名添加进去，如下面：

```
127.0.0.1 localhost
${ip}  ${hostname}
```

### 6.3 SSH 配置问题

查看控制台日志，报 `connect server error.Auth fail for methods 'publickey,gssapi-keyex,gssapi-with-mic,password'` 错误，可以修改服务器的 `sshd_config` 配置文件：

```bash
$ vi /etc/ssh/sshd_config
```

关闭 GSSAPI 认证方式：

```ini
# GSSAPI options (将 yes 改成 no)
GSSAPIAuthentication yes
GSSAPICleanupCredentials no
```

然后重启 `sshd` 服务：

```bash
$ systemctl restart sshd
```

---

### 6.4 未开启 SFTP

查看控制台报错日志显示如下：

```
[]开始进行连接测试：[**********]
[]connect server error.failed to send channel request
```

查看服务器的 `sshd_config` 配置文件：

```bash
$ vi /etc/ssh/sshd_config
```

确认是否开启了 SFTP 功能，配置类似下面这样：

```
# override default of no subsystems
Subsystem sftp  /usr/libexec/openssh/sftp-server
```

确保这一行前面没有注释，同时检查 `sftp-server` 文件是否存在。修改后重启 `sshd` 服务即可。

### 6.5 SSH 连接超时

如果查看日志，显示如下的超时错误：

```
connect server error.timeout: socket is not establish
```

可以尝试修改 SSH 连接的超时时间：

```
skynet.xmanager.ssh.timeout.seconds=8
```

然后重启 xmanager 后，删除节点，重新注册节点。

## 7、manager 或 agent 启动失败常见异常处理

### 7.1 v3.4.4(含) 以前版本报 ip=********** 未注册

启动时报错如下：

```text
antServerParam is null.this server-node [ip=**********] is no registration or deleted.
```

这是因为 Skynet 启动时获取本地 IP 地址时，拿到的是 docker0 网卡的地址，可以在 `$SKYNET_HOME/conf/application.properties` 中的增加 `spring.cloud.inetutils.ignored-interfaces` 配置项，将 docker0 和虚拟网卡排除掉：

```properties
spring.cloud.inetutils.ignored-interfaces=docker0,veth.*
``` 

然后重启 manager 或 agent 即可。

如果是节点注册后无法启动，也可能是由于注册的 IP 和服务器真实 IP 不一致导致的，确保注册的 IP 不是虚拟 IP 而是服务器的真实 IP。

### 7.2 报 Zookeeper 连接错误

启动时报错如下：

```text
KeeperErrorCode = Session closed because client failed to authenticate for /skynet/cluster/topology/***********
```

说明 Zookeeper 开启了认证，但是 manager 或 agent 的配置文件中没有开启认证，修改 `$SKYNET_HOME/conf/skynet.properties` 配置文件，将下面的一行取消注释：

```properties
java.security.auth.login.config=${SKYNET_HOME}/conf/skynet.zk.jaas.cfg
```

### 7.3 启动报 `Current process will exit` 错误

首先，检查服务器的 `/etc/hosts`，是否存在错误的配置项，如果没有，则尝试将服务器的 IP 和主机名添加进去。

如果还不行，尝试修改 `$SKYNET_HOME/conf/skynet.properties` 配置文件，添加如下配置：

```
skynet.system.pid.timeout=30000
```

### 7.4 启动报 `Cannot assign requested address` 错误

Skynet Agent 无法启动，报错日志如下：

```
Caused by: org.apache.catalina.LifecycleException: Protocol handler start failed
        at org.apache.catalina.connector.Connector.startInternal(Connector.java:1042)
        at org.apache.catalina.util.LifecycleBase.start(LifecycleBase.java:183)
        at org.apache.catalina.core.StandardService.addConnector(StandardService.java:232)
        ... 17 common frames omitted
Caused by: java.net.BindException: Cannot assign requested address
        at sun.nio.ch.Net.bind0(Native Method)
        at sun.nio.ch.Net.bind(Net.java:444)
```

检查 `$SKYNET_HOME/conf/application.properties` 配置文件，是否有写死的 IP 地址：

```
server.address=XXX
skynet.ipAddress=XXX
```

如果有，说明在部署 Skynet Xmanager 的时候修改过配置文件，将 `XXX` 修改为正确的 IP 或 `${IP}` 变量即可。

> 注：不建议修改 Xmanager 配置文件中的 IP 地址，该配置文件会分发到其他的 Xagent 节点。可以在启动命令行参数中指定 IP 地址，比如 `ant-xmanager.sh daemon ***************`。

### 7.5 启动报 `KeeperErrorCode = Unimplemented for /skynet` 错误

Skynet Manager 无法启动，报错日志如下：

```
2024-11-11 19:13:15.873  INFO 2164239 --- [           main] s.p.m.config.ZooKeeperConfigChecker      : Auto import skynet zk config to zkServer:[**************:2181,**************:2181,**************:2181],[clusterName=skynet] ...
2024-11-11 19:13:15.874  INFO 2164239 --- [           main] s.p.m.config.ZooKeeperConfigChecker      : Load zk configLocation=/data/CBB/skynet/doc/skynet.zk.config to /skynet...
2024-11-11 19:13:16.139 ERROR 2164239 --- [           main] s.p.c.c.SkynetPropertySourceLocator      : Config system properties error= KeeperErrorCode = Unimplemented for /skynet

org.apache.zookeeper.KeeperException$UnimplementedException: KeeperErrorCode = Unimplemented for /skynet
	at org.apache.zookeeper.KeeperException.create(KeeperException.java:106)
	at org.apache.zookeeper.KeeperException.create(KeeperException.java:54)
	at org.apache.zookeeper.ZooKeeper.create(ZooKeeper.java:1837)
	at org.apache.curator.framework.imps.CreateBuilderImpl$18.call(CreateBuilderImpl.java:1216)
	at org.apache.curator.framework.imps.CreateBuilderImpl$18.call(CreateBuilderImpl.java:1193)
	at org.apache.curator.RetryLoop.callWithRetry(RetryLoop.java:93)
	at org.apache.curator.framework.imps.CreateBuilderImpl.pathInForeground(CreateBuilderImpl.java:1190)
	at org.apache.curator.framework.imps.CreateBuilderImpl.protectedPathInForeground(CreateBuilderImpl.java:605)
	at org.apache.curator.framework.imps.CreateBuilderImpl.forPath(CreateBuilderImpl.java:595)
	at org.apache.curator.framework.imps.CreateBuilderImpl$4.forPath(CreateBuilderImpl.java:455)
	at org.apache.curator.framework.imps.CreateBuilderImpl$4.forPath(CreateBuilderImpl.java:391)
	at skynet.boot.zookeeper.impl.ZkConfigServiceImpl.putNode(ZkConfigServiceImpl.java:256)
	at skynet.boot.zookeeper.impl.ZkConfigServiceImpl.createPathAndNode(ZkConfigServiceImpl.java:495)
	at skynet.boot.zookeeper.impl.ZkConfigServiceImpl.importData(ZkConfigServiceImpl.java:438)
	at skynet.platform.manager.config.ZooKeeperConfigChecker.check(ZooKeeperConfigChecker.java:80)
	at skynet.platform.manager.config.ManagerPropertySourceLocator.onLocate(ManagerPropertySourceLocator.java:26)
	at skynet.platform.common.config.SkynetPropertySourceLocator.locate(SkynetPropertySourceLocator.java:31)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:51)
	at org.springframework.cloud.bootstrap.config.PropertySourceLocator.locateCollection(PropertySourceLocator.java:47)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.doInitialize(PropertySourceBootstrapConfiguration.java:120)
	at org.springframework.cloud.bootstrap.config.PropertySourceBootstrapConfiguration.initialize(PropertySourceBootstrapConfiguration.java:110)
	at org.springframework.boot.SpringApplication.applyInitializers(SpringApplication.java:604)
	at org.springframework.boot.SpringApplication.prepareContext(SpringApplication.java:373)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:306)
	at skynet.boot.AppUtils.run(AppUtils.java:33)
	at skynet.platform.manager.Bootstrap.main(Bootstrap.java:42)
```

这个错一般是 ZooKeeper 版本过低导致的，确保 ZooKeeper 必须是 3.6.3 及以上版本。

### 7.6 启动报 `Could not initialize class com.sun.jna.Native` 错误

服务节点注册成功，但是启动报错，报错信息如下：

![](./asserts/jna-no-class.png)

使用 `df -h` 检查磁盘空间：

![](./asserts/df-h.jpeg)

确保 `/` 挂载点下有足够空间（这个错比较隐晦，由于 Skynet Agent 启动时会通过 OSHI 获取 CPU 和内存等系统信息，这个是依赖于 JNA 实现的，而 JNA 会在 `/` 目录下生成临时文件，当生成失败时就会报这个错，所以需要保证 `/` 挂载点下有一定的空间）。

---

## 8、多网卡服务器 manager 启动后不是自己预期的IP，如何排查与修改？

可以通过命令 `ip -a` 确认期望的 IP 网卡序号是否高于当前的 IP 地址网卡(一般是外置网卡情况下)，如：

![](../asserts/ip-a.png)

可以通过 xmanager 启动命令行指定期望的 IP：

```shell
# ant-xmanager.sh (start|debug|daemon) [ip]
$ ./ant-xmanager.sh daemon ***************
```
---

## 9、Skynet 忘记登录密码

登录 ZKUI 页面，默认地址为 `http://ip:9090`，默认用户名密码为 `admin/manager`，进入 `/skynet/xmanager/users` 路径，将 `admin` 的密码重置为 `d8700420aa20b4aa6bb1ea8c35baa727` 即可，这个值对应默认的密码 `Skynet@2230`。

如果没有部署 ZKUI，可以直接在服务器上操作 ZK：

```
$ cd /path/to/zookeeper/bin/
$ ./zkCli.sh -server 127.0.0.1:2181
[zk: 127.0.0.1:2181(CONNECTED) 0] get /skynet/xmanager/users/admin
[zk: 127.0.0.1:2181(CONNECTED) 1] set /skynet/xmanager/users/admin d8700420aa20b4aa6bb1ea8c35baa727
```

---

## 10、服务定义中的功能选项缺失

服务定义中的功能选项可以在 ZKUI 中进行配置，登录 ZKUI 页面，进入 `/skynet/xmanager/action` 路径，修改 `label` 的值即可：

```json
[
    {
        "title": "调式模式",
        "code": "debug",
        "extPlaceHolder": "",
        "extTitle": "",
        "boots": []
    },
    {
        "title": "显示[Actuator]",
        "code": "enableActuator",
        "extPlaceHolder": "例如 [base64(user:pwd)@]/actuator",
        "extTitle": "Actuator访问配置",
        "boots": []
    },
    {
        "title": "启用监控采集[Prometheus]",
        "code": "enablePrometheusTarget",
        "extPlaceHolder": "例如 [base64(user:pwd)@]/metrics:15s",
        "extTitle": "Prometheus采集点配置",
        "boots": []
    },
    {
        "title": "启用Ant追踪[Skywalking]",
        "code": "enableSkywalking",
        "extPlaceHolder": "",
        "extTitle": "",
        "boots": [
            "SpringBoot",
            "SkynetBoot"
        ]
    },
    {
        "title": "启用Nginx网关",
        "code": "enableNginxGateway",
        "extPlaceHolder": "请输入服务别名",
        "extTitle": "服务别名",
        "boots": []
    },
    {
        "title": "参与流程计算(分布式计算平台)",
        "code": "enableStream",
        "extPlaceHolder": "",
        "extTitle": "",
        "boots": []
    },
    {
        "title": "启用服务命名空间",
        "code": "SERVICE_DISCOVERY_NAMESPACE",
        "extPlaceHolder": "如 turing-ai，多个用分号分隔",
        "extTitle": "命名空间",
        "boots": []
    },
    {
        "title": "禁用追加JVM参数",
        "code": "disableAppendJvmOptions",
        "extPlaceHolder": "",
        "extTitle": "",
        "boots": []
    }
]
```

## 11、如何升级 Docker 版本？

Skynet 内置的 Docker 版本为 20.10.9，如果希望升级成其他版本，可以使用 Skynet 自带的工具来更新：

> Skynet 3.4.12 之后升级到 Docker 20.10.24

1. 从下面的地址下载对应版本的 Docker 安装包，放在 `$SKYNET_HOME/runtime` 目录下；

* x86版本：https://download.docker.com/linux/static/stable/x86_64/
* arm版本：https://download.docker.com/linux/static/stable/aarch64/

2. 执行下面的命令，卸载 Docker 服务；注意一定要在 `$SKYNET_HOME` 目录下执行；

```
$ cd $SKYNET_HOME
$ ./runtime/docker-install.sh -u
$ rm -rf /usr/bin/containerd*
$ rm -rf /usr/bin/docker*
$ rm -rf /usr/bin/ctr
$ rm -rf /usr/bin/runc
```

3. 修改 `docker-install.sh` 文件的第 26 行，将文件名改成刚刚下载的文件；

```
FILETARGZ=./runtime/docker-20.10.9.tgz
```

4. 执行下面的命令，安装新版本的 Docker；注意一定要在 `$SKYNET_HOME` 目录下执行；

```
cd $SKYNET_HOME
$ ./runtime/docker-install.sh -i
```

5. 使用 `docker -v` 确认新版本 Docker 是否安装完成；如果一切正常，将服务器上停掉的 Docker 容器重新启动即可。

## 12. 页面显示乱码

Skynet 安装后能正常访问和登录，但是页面上部分中文显示乱码，如下图所示：

![](./asserts/skynet-lang.png)

这大概率是由于操作系统的语言设置导致的，请确保操作系统的语言设置为 `C.UTF-8`、`en_US.UTF-8` 或 `zh_CN.UTF-8`：

```
$ echo $LANG
```

如果不是的话，可以通过下面的命令进行修改：

```
$ export LANG=zh_CN.UTF-8
```

或修改 `/etc/locale.conf` 配置文件，然后停止 ZooKeeper 和 Skynet，将 ZooKeeper 的数据清空（删除 data 目录），再重新启动 ZooKeeper 和 Skynet 即可。

## 13. 如何修复 CORS 信任任意来源漏洞？

修改 `$SKYNET_HOME/conf/application.properties` 中的 `webshell` 相关的配置，然后重启 xmanager 即可：

```properties
skynet.webshell.allowedOriginPatterns=http://${IP}:${PORT}
```

如果有代理或映射到外网，可能多个地址，使用逗号分隔：

```properties
skynet.webshell.allowedOriginPatterns=http://${IP}:${PORT},http://your-other-ip:port
``` 

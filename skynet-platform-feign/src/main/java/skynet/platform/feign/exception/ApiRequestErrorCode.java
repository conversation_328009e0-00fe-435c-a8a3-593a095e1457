package skynet.platform.feign.exception;

/**
 * Xmanager api接口错误码类
 * <p>
 * code为整型，错误信息为中文；
 * 错误码设计原则：1. 接口用户能看得懂 2. 能帮助（用户或技术）定位问题
 * <p>
 *
 * <AUTHOR> by jianwu6 on 2020/7/31 11:42
 */
public enum ApiRequestErrorCode {
    /**
     * 成功
     */
    SUCCESS("success", 0),


    /**
     *
     */
    UNKNOWN_ERROR("未知的错误", 500),

    /************ 参数相关 ************/
    PARAM_ILLEGAL("非法的请求参数", 600),
    PARAM_ACTION_CODE_ILLEGAL("服务编码必须是数字字母或者中划线下划线", 601),
    UNSUPPORTED_TYPE("不支持的 type 类型", 602),
    MISSING_PARAMETER("缺少请求参数", 603),
    BLANK_IP_ADDRESS("ip 地址是空", 604),
    NULL_PARAM("参数不能为空", 605),
    ACTION_WITH_PLUGIN_EQUAL("服务编码与应用系统编码不能相同", 606),


    /************ plugin 相关 ************/
    PLUGIN_BLANK("系统编码为空", 1001),
    PLUGIN_NOT_EXIST("系统不存在", 1002),
    PLUGIN_ALREADY_EXIST("系统已经存在", 1005),

    /************ action 相关 ************/
    ACTION_BLANK("服务编码为空", 2001),
    ACTION_NOT_EXIST("服务不存在", 2002),
    ACTION_NOT_IN_AGENT("服务未分配在服务器上", 2003),
    ACTION_NUMBER_LIMIT("服务数量必须必须在 1-16 之间", 2004),
    ACTION_ALREADY_EXIST("服务已经存在", 2005),


    /************ agent 相关 ************/
    AGENT_NOT_REGISTER("服务器未注册", 3001),
    AGENT_SSH_ERROR("服务器SSH连接失败", 3002),
    AGENT_K8S_ERROR("Kubernetes连接失败", 3003),
    AGENT_REGISTRY_ERROR("镜像仓库连接失败", 3004),

    /********** user 相关 ************/

    USER_NOT_EXIST("用户不存在", 5001),
    USER_ALREADY_EXIST("用户已经存在", 5002),
    USER_AUTH_INVALID("用户名或原始密码错误", 5004),
    USER_PASSWORD_INVALID("密码强度不够（8位以上包含大小写字母、数字、特殊字符）", 5006),


    DIAGNOSIS_INDEX_OUTRANGE_ERROR("分组序号越界", 8002);

    private final int code;
    private final String value;

    ApiRequestErrorCode(String value, int code) {
        this.value = value;
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }
}

package skynet.platform.feign.exception;

/**
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2018年10月30日 下午8:28:00]
 */
public class ActionAlreadyExistException extends ApiRequestException {


    private final String actionPoint;

    /**
     * @return the actionPoint
     */
    public String getActionPoint() {
        return actionPoint;
    }


    public ActionAlreadyExistException(String actionPoint) {
        super(ApiRequestErrorCode.ACTION_ALREADY_EXIST);
        this.actionPoint = actionPoint;
    }


    @Override
    public String getMessage() {
        return String.format("%s[actionPoint=%s]", super.getMessage(), actionPoint);
    }
}


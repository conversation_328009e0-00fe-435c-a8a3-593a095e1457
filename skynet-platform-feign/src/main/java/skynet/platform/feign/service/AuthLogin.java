package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.Getter;
import lombok.Setter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import skynet.boot.common.domain.Jsonable;
import skynet.platform.feign.config.AuthSignConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.AccessToken;
import skynet.platform.feign.model.SkynetApiResponse;


/***
 *  用户登录管理
 *
 * <AUTHOR>
 */
@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthSignConfig.class, contextId = "AuthLogin")
@Tag(name = "v0.系统登录验证", description = "系统登录验证")//, hidden = true)
public interface AuthLogin {

    String PREFIX = "/skynet/auth";

    @PostMapping(value = PREFIX + "/login", produces = {MediaType.APPLICATION_JSON_VALUE}, consumes = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "登录")
    @Parameter(name = "loginInfo", required = true, description = "用户名、密码")
    SkynetApiResponse<AccessToken> login(@RequestBody LoginInfo loginInfo);

    @Operation(summary = "修改密码")
    @PostMapping(value = PREFIX + "/updatePwd", produces = {MediaType.APPLICATION_JSON_VALUE}, consumes = {MediaType.APPLICATION_JSON_VALUE})
    SkynetApiResponse<Void> updatePwd(@RequestBody UpdatePasswordInfo info) throws Exception;

    /**
     * 增加用户
     * <p>
     * 此接口 需要利用 skynet.security.api-auth 鉴权访问
     *
     * @param loginInfo
     * @return
     * @throws Exception
     */
    @Operation(summary = "添加用户")
    @PostMapping(value = PREFIX + "/addUser", produces = {MediaType.APPLICATION_JSON_VALUE}, consumes = {MediaType.APPLICATION_JSON_VALUE})
    SkynetApiResponse<Void> addUser(@RequestBody LoginInfo loginInfo) throws Exception;

    /**
     * 重置密码
     * <p>
     * 此接口 需要利用 skynet.security.api-auth 鉴权访问
     *
     * @param loginInfo
     * @return
     * @throws Exception
     */
    @Operation(summary = "重置密码")
    @PostMapping(value = PREFIX + "/resetPwd", produces = {MediaType.APPLICATION_JSON_VALUE}, consumes = {MediaType.APPLICATION_JSON_VALUE})
    SkynetApiResponse<Void> resetPwd(@RequestBody LoginInfo loginInfo) throws Exception;

    /**
     * 获取 缺省用户 token ，用于免登陆
     * <p>
     * 此接口 需要利用 skynet.security.api-auth 鉴权访问
     *
     * @return
     */
    @Operation(summary = "缺省用户token")
    @GetMapping(value = PREFIX + "/token", produces = {MediaType.APPLICATION_JSON_VALUE})
    SkynetApiResponse<AccessToken> token();

    @Getter
    @Setter
    class UpdatePasswordInfo extends Jsonable {
        @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
        private String username;
        @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
        private String password;
        @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
        private String newpassword;

        @Override
        public String toString() {
            return super.toString();
        }
    }

    @Getter
    @Setter
    class LoginInfo extends Jsonable {
        @Schema(requiredMode = Schema.RequiredMode.REQUIRED, description = "user-name")
        private String username;
        @Schema(requiredMode = Schema.RequiredMode.REQUIRED)
        private String password;

        @Override
        public String toString() {
            return super.toString();
        }
    }
}

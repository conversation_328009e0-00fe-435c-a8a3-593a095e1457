package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.annotations.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.NoDataResponse;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.model.SwitchLabelTypeDto;

import java.util.List;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3SwitchLabelType")
@Tag(name = "v3. 服务功能开关项类型管理", description = "服务功能开关项类型管理")//, hidden = true)
public interface V3SwitchLabelType {

    String PREFIX = "/skynet/api/v3/actions/switch-labels";

    @GetMapping(value = PREFIX, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取所有开关选项类型")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<SwitchLabelTypeDto>> getAll();

    @PostMapping(value = PREFIX, produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "新增开关选项类型")
    //@ApiResponse(code = 200, message = "成功")
    NoDataResponse create(@RequestBody SwitchLabelTypeDto request);

    @PutMapping(value = PREFIX + "/{code}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "更新开关选项类型")
    //@ApiResponse(code = 200, message = "成功")
    NoDataResponse create(@PathVariable String code,
                          @RequestBody SwitchLabelTypeDto request);

    @DeleteMapping(value = PREFIX + "/{code}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除开关选项类型")
    //@ApiResponse(code = 200, message = "成功")
    NoDataResponse create(@PathVariable String code);
}

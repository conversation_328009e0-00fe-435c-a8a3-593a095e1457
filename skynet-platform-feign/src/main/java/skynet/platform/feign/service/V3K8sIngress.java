package skynet.platform.feign.service;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import com.alibaba.fastjson2.JSONObject;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.annotations.ApiResponse;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.config.AuthTokenConfig;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sIngress")
@Tag(name = "v3. K8S Ingress 管理", description = "K8S Ingress 管理")//, hidden = true)
public interface V3K8sIngress {
    
    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/ingresses", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Ingress 列表")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<JSONObject>> getIngresss(@PathVariable String ip, @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/ingresses/{ingressName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Ingress 详情")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> getIngress(@PathVariable String ip, @PathVariable String namespace, @PathVariable String ingressName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/ingresses/{ingressName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 Ingress Yaml")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> getIngressYaml(@PathVariable String ip, @PathVariable String namespace, @PathVariable String ingressName) throws Exception;

    @DeleteMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/ingresses/{ingressName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除 Ingress")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> deleteIngress(@PathVariable String ip, @PathVariable String namespace, @PathVariable String ingressName) throws Exception;
}

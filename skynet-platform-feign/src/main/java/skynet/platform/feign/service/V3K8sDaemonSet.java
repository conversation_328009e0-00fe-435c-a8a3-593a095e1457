package skynet.platform.feign.service;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.alibaba.fastjson2.JSONObject;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.annotations.ApiResponse;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.K8sUpdateImage;
import skynet.platform.feign.model.K8sUpdateDaemonSetStrategy;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.config.AuthTokenConfig;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sDaemonSet")
@Tag(name = "v3. K8S DaemonSet 管理", description = "K8S DaemonSet 管理")//, hidden = true)
public interface V3K8sDaemonSet {
    
    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/daemonsets", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 DaemonSet 列表")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<JSONObject>> getDaemonSets(@PathVariable String ip, @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/daemonsets/{daemonSetName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 DaemonSet 详情")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> getDaemonSet(@PathVariable String ip, @PathVariable String namespace, @PathVariable String daemonSetName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/daemonsets/{daemonSetName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 DaemonSet Yaml")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> getDaemonSetYaml(@PathVariable String ip, @PathVariable String namespace, @PathVariable String daemonSetName) throws Exception;

    @DeleteMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/daemonsets/{daemonSetName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除 DaemonSet")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> deleteDaemonSet(@PathVariable String ip, @PathVariable String namespace, @PathVariable String daemonSetName) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/daemonsets/{daemonSetName}/restart", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "重启")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> restartDaemonSet(@PathVariable String ip, @PathVariable String namespace, @PathVariable String daemonSetName) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/daemonsets/{daemonSetName}/images", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "调整镜像版本")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> updateDaemonSetImages(@PathVariable String ip, @PathVariable String namespace, @PathVariable String daemonSetName, @RequestBody K8sUpdateImage k8sUpdateImage) throws Exception;

    @PostMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/daemonsets/{daemonSetName}/strategy", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "修改更新策略")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> updateDaemonSetStrategy(@PathVariable String ip, @PathVariable String namespace, @PathVariable String daemonSetName, @RequestBody K8sUpdateDaemonSetStrategy k8sUpdateDaemonSetStrategy) throws Exception;
}

package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.annotations.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.ClusterInfoDto;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.Map;

/**
 * <AUTHOR>
 */
@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3Cluster")
@Tag(name = "v3. 集群信息接口", description = "获取集群信息")//, hidden = true)
public interface V3Cluster {

    String PREFIX = "/skynet/api/v3/cluster";

    @GetMapping(value = PREFIX + "/info", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取获取集群信息")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<Map<String, Object>> getInfo();

    @GetMapping(value = PREFIX + "/properties", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取集群级属性配置")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> getProperties();

    @GetMapping(value = PREFIX + "/logging-levels", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取集群级logback日志级别配置")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> getLoggingLevels();

    @PutMapping(value = PREFIX + "/properties", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "更新集群级属性配置")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> updateProperties(@RequestBody(required = false) String request);

    @PutMapping(value = PREFIX + "/logging-levels", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "更新集群级logback日志级别配置")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> updateLoggingLevels(@RequestBody(required = false) String request);

    class GetClusterInfoResponse extends SkynetApiResponse<ClusterInfoDto> {
    }
}

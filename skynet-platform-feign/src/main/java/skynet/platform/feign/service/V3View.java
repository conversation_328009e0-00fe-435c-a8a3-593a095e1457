package skynet.platform.feign.service;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.annotations.ApiResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.GrafanaAddress;
import skynet.platform.feign.model.MenuView;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3View")
@Tag(name = "v3. 视图渲染相关接口", description = "视图渲染相关接口")//, hidden = true)
public interface V3View {

    String PREFIX = "/skynet/api/v3/view";

    @PutMapping(value = PREFIX + "/order", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "更新顺序")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<Void> adjustOrder(@Parameter(description = "实体类型,支持 agent/plugin/action-definition") @RequestParam(name = "type") String type,
                                        @Parameter(description = "服务插件 仅在 type = action-definition 时需要传") @RequestParam(name = "plugin", required = false) String plugin,
                                        @RequestBody List<String> order) throws Exception;

    @GetMapping(value = PREFIX + "/menus", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取动态菜单")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<List<MenuView>> getMenus();

    @GetMapping(value = PREFIX + "/grafana/address", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取Grafana地址")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<GrafanaAddress> getGrafanaAddr();
}

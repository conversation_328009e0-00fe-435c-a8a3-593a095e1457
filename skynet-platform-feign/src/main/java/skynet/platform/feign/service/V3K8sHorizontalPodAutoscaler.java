package skynet.platform.feign.service;

import com.alibaba.fastjson2.JSONObject;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import skynet.platform.feign.config.AuthTokenConfig;
import skynet.platform.feign.config.SkynetConst;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.SkynetApiResponse;

import java.util.List;

@FeignClient(value = SkynetConst.SKYNET_MANAGER_SERVICE_ID, configuration = AuthTokenConfig.class, contextId = "V3K8sHorizontalPodAutoscaler")
@Tag(name = "v3. K8S HorizontalPodAutoscaler 管理", description = "K8S HorizontalPodAutoscaler 管理" )
public interface V3K8sHorizontalPodAutoscaler {
    
    String PREFIX = "/skynet/api/v3/k8s";

    @GetMapping(value = PREFIX + "/{ip}/horizontalpodautoscalers", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 HorizontalPodAutoscaler 列表") 
    SkynetApiResponse<List<JSONObject>> getHorizontalPodAutoscalers(@PathVariable String ip, @SpringQueryMap K8sQuery k8sQuery) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/horizontalpodautoscalers/{hpaName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 HorizontalPodAutoscaler 详情")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> getHorizontalPodAutoscaler(@PathVariable String ip, @PathVariable String namespace, @PathVariable String hpaName) throws Exception;

    @GetMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/horizontalpodautoscalers/{hpaName}/yaml", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "获取 HorizontalPodAutoscaler Yaml")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<String> getHorizontalPodAutoscalerYaml(@PathVariable String ip, @PathVariable String namespace, @PathVariable String hpaName) throws Exception;

    @DeleteMapping(value = PREFIX + "/{ip}/namespaces/{namespace}/horizontalpodautoscalers/{hpaName}", produces = {MediaType.APPLICATION_JSON_VALUE})
    @Operation(summary = "删除 HorizontalPodAutoscaler")
    //@ApiResponse(code = 200, message = "成功")
    SkynetApiResponse<JSONObject> deleteHorizontalPodAutoscaler(@PathVariable String ip, @PathVariable String namespace, @PathVariable String hpaName) throws Exception;
}

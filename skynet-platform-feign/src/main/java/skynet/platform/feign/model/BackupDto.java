package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.List;

@Getter
@Setter
@Schema(title =  "备份信息")
public class BackupDto extends Jsonable {

    @Schema(title = "备份名")
    private String name;

    @Schema(title = "系统级配置")
    private List<BackupPluginVo> plugins;

    @Schema(title = "集群级配置")
    private BackupClusterVo cluster;

    @Getter
@Setter
    @Schema(title =  "系统级配置")
    public static class BackupPluginVo {

        @Schema(title = "插件名称")
        private String name;

        @Schema(title = "插件编码")
        private String code;

        @Schema(title = "插件的服务定义")
        private String content;
    }

    @Getter
@Setter
    @Schema(title =  "集群级配置")
    public static class BackupClusterVo {

        @Schema(title = "属性配置")
        private String properties;

        @Schema(title = "日志配置")
        private String loggingLevels;
    }
}

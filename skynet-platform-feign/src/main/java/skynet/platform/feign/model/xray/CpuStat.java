package skynet.platform.feign.model.xray;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * CPU 运行状态数据 [动态]
 *
 * <pre>
 * us：用户态使用的cpu时间比
 * sy：系统态使用的cpu时间比
 * ni：用做nice加权的进程分配的用户态cpu时间比
 * id：空闲的cpu时间比
 * wa：cpu等待磁盘写入完成时间hi：硬中断消耗时间
 * si：软中断消耗时间
 * st：虚拟机偷取时间
 *
 * http://www.cnblogs.com/yjf512/p/3383915.html
 * </pre>
 *
 * <AUTHOR> [2016年8月3日上午9:35:59]
 */
@Getter
@Setter
public class CpuStat extends PerfStatBase {

    private String model;
    private int mhz;
    private int cpuNum;
    private int coresPerCpu;

    @Schema(title = "xray_cpu_cores_total")
    private int coresTotal;
    /**
     * 使用的核数
     */
    @Schema(title = "xray_cpu_used_cores")
    private double usedcores;

    @Schema(title = "xray_cpu_user")
    private double user;
    @Schema(title = "xray_cpu_cores_system")
    private double system;
    @Schema(title = "xray_cpu_cores_nice")
    private double nice;
    @Schema(title = "xray_cpu_cores_wait")
    private double wait;

    /**
     * 空闲百分比（范围: 0-1.0）
     */
    @Schema(title = "xray_cpu_idle")
    private double idle;

    /**
     * 使用百分比（范围: 0-1.0）
     */
    @Schema(title = "xray_cpu_combined")
    private double combined;

    private double[] loadAvg;

    @Override
    public String toString() {
        return super.toString();
    }
}

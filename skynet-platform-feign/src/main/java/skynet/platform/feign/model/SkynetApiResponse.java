package skynet.platform.feign.model;

import com.alibaba.fastjson2.JSON;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.exception.message.ErrorMessage;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@Schema(title = "服务响应")
public class SkynetApiResponse<T> implements ErrorMessage {

    @Schema(title = "状态码, 0:成功， 非0：失败")//, position = 1)
    private int code;

    @Schema(title = "错误信息")//, position = 2)
    private String message = "";

    @Schema(title = "数据")//, position = 3)
    private T data;

    public SkynetApiResponse() {
    }

    public SkynetApiResponse(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public SkynetApiResponse(int code, T data) {
        this.code = code;
        this.data = data;
    }

    public static <T> SkynetApiResponse<T> success() {
        return instance(ApiRequestErrorCode.SUCCESS.getCode(), ApiRequestErrorCode.SUCCESS.getValue(), null);
    }

    public static <T> SkynetApiResponse<T> success(T data) {
        return instance(ApiRequestErrorCode.SUCCESS.getCode(), ApiRequestErrorCode.SUCCESS.getValue(), data);
    }

    public static <T> SkynetApiResponse<T> success(String message, T data) {
        return instance(ApiRequestErrorCode.SUCCESS.getCode(), message, data);
    }

    public static <T> SkynetApiResponse<T> fail(int code, String message) {
        return instance(code, message, null);
    }

    public static <T> SkynetApiResponse<T> fail(Exception e) {
        String message = "";
        int code;
        if (e instanceof ApiRequestException) {
            code = ((ApiRequestException) e).getCode();
            message = e.getMessage();
        } else {
            code = ApiRequestErrorCode.UNKNOWN_ERROR.getCode();
            message = ApiRequestErrorCode.UNKNOWN_ERROR.getValue();
        }
        return instance(code, message, null);
    }

    public static <T> SkynetApiResponse<T> fail(ApiRequestErrorCode e) {
        return instance(e.getCode(), e.getValue(), null);
    }

    public static <T> SkynetApiResponse<T> instance(int code, String message, T data) {
        SkynetApiResponse<T> s = new SkynetApiResponse<>();
        s.setCode(code);
        s.setMessage(message);
        s.setData(data);
        return s;
    }

    public void setException(Exception e) {
        if (e instanceof ApiRequestException) {
            this.code = ((ApiRequestException) e).getCode();
            this.message = e.getMessage();
        } else {
            this.code = ApiRequestErrorCode.UNKNOWN_ERROR.getCode();
            this.message = ApiRequestErrorCode.UNKNOWN_ERROR.getValue();
        }
    }

    @Override
    public String getPath() {
        return "";
    }


    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }

    @Override
    public String getTraceId() {
        return null;
    }
}

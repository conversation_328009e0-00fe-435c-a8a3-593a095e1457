package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.Map;

@Getter
@Setter
@Schema(title =  "部署服务请求")
public class ActionDeploymentUpdateDto extends Jsonable {

    @Schema(title = "服务坐标")//, position = 10)
    private String actionPoint;

    @Schema(title = "实例数量")//, position = 20)
    private String num;

    @Schema(title = "副本数")//, position = 22)
    private int replicas=1;

    @Schema(title = "标签")//, position = 25)
    private Map<String, String> nodeSelector;

    @Schema(title = "顺序")//, position = 30)
    private int order;

    @Schema(title = "启用状态")//, position = 40)
    private boolean enabled = true;

    @Override
    public String toString() {
        return super.toString();
    }
}

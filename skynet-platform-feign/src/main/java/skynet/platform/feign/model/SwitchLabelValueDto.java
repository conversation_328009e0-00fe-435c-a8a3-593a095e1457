package skynet.platform.feign.model;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(title =  "功能开关选项值")
public class SwitchLabelValueDto {

    @Schema(title = "功能编码")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 10)
    private String code;

//    @Schema(title = "功能名称")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 20)
//    private String name;

    @Schema(title = "是否开启")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 30)
    private boolean value;

    @Schema(title = "扩展属性值")//, requiredMode = Schema.RequiredMode.REQUIRED, position = 40)
    private String extProperty;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

//    public String getName() {
//        return name;
//    }
//
//    public void setName(String name) {
//        this.name = name;
//    }

    public boolean isValue() {
        return value;
    }

    public void setValue(boolean value) {
        this.value = value;
    }

    public String getExtProperty() {
        return extProperty;
    }

    public void setExtProperty(String extProperty) {
        this.extProperty = extProperty;
    }

//    public static AntActionLabel toAntActionLabel(SwitchLabelValueDto dto){
//        AntActionLabel ret = new AntActionLabel();
//        ret.setCode(dto.getCode());
//        ret.setExtProperty(dto.getExtProperty());
//        ret.setValue(dto.isValue());
//        return ret;
//    }
}

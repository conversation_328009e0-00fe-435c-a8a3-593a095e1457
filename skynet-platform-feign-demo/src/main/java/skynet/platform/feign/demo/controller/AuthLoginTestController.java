package skynet.platform.feign.demo.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.platform.feign.model.AccessToken;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.AuthLogin;

/**
 * <AUTHOR>
 * @date 2020/11/29 13:29
 */
@Slf4j
@RestController
@RequestMapping(produces = {MediaType.APPLICATION_JSON_VALUE})
public class AuthLoginTestController {

    private final AuthLogin authLogin;

    public AuthLoginTestController(AuthLogin authLogin) {
        this.authLogin = authLogin;
    }

    @GetMapping("/")
    public Object get() {
        SkynetApiResponse<AccessToken> tokenRestResponse = authLogin.token();
        return tokenRestResponse;
    }

    @GetMapping("/addUser")
    public Object addUser() throws Exception {
        AuthLogin.LoginInfo loginInfo = new AuthLogin.LoginInfo();
        loginInfo.setUsername("lyhu");
        loginInfo.setPassword("P@ssword1");
        SkynetApiResponse<Void> response = authLogin.addUser(loginInfo);
        log.debug("1.addUser={}", response);
        response = authLogin.addUser(loginInfo);
        log.debug("2.addUser={}", response);
        return response;
    }

    @GetMapping("/resetPwd")
    public Object resetPwd() throws Exception {
        AuthLogin.LoginInfo loginInfo = new AuthLogin.LoginInfo();
        loginInfo.setUsername("lyhu");
        loginInfo.setPassword("Skynet@2230");
        SkynetApiResponse<Void> response = authLogin.resetPwd(loginInfo);
        log.debug("1.resetPwd={}", response);
        return response;
    }
}

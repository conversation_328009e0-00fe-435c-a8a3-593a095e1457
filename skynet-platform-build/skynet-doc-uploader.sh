#!/doc/bash

echo '---------------------------------------------'
echo 'upload markdown to server begin ...'

# 在此配置服务接口
upload_url="http://turing.iflytek.com:2230/skynet-doc/upload"

# 在此配置文件映射关系
# 格式："{本地文件相对路径} {文档中心文件相对路径和文件名}"
# 中间以空格隔开，多个映射关系用多行表示
files=(
    "misc/doc/CHANGELOG.md skynet-platform/changelog.md"
)

DIR=$(cd `dirname $0`;pwd)
# 通知服务拉取代码
curl -X GET $upload_url
# 上传文件
for file in "${files[@]}"; do
    instance=($file)
    local=${instance[0]}
    remote=${instance[1]}
    if [ -f $DIR/$local ]; then
        local=$DIR/$local
        echo    
        echo "begin post $local  to $upload_url .."
        curl -X POST -H "Content-Type: multipart/form-data" -F "remote_path=$remote" -F "file=@$local" $upload_url
    else
       echo "not found file: $DIR/$local"
    fi 
done
# 通知服务提交文件
echo  
curl -X PUT $upload_url
echo  
echo 'upload markdown to server end.'
echo '---------------------------------------------'
//import java.io.BufferedWriter;
//import java.io.File;
//import java.io.FileOutputStream;
//import java.io.IOException;
//import java.io.OutputStreamWriter;
//import java.nio.file.Paths;
//import java.util.ArrayList;
//import java.util.List;
//
//public class VersionGenerator {
//
//    public static void main(String[] args) {
//        String buildDir = System.getProperty("build.dir", ".");
//        if (buildDir == null || buildDir.isEmpty()) {
//            System.out.println("build.dir does not exists,exit");
//        }
//        String buildTime = System.getProperty("build.time", "UNKNOWN");
//        String buildBranch = System.getProperty("build.branch", "UNKNOWN");
//        String buildNum = System.getProperty("build.num", "UNKNOWN");
//        List<String> lines = new ArrayList<String>();
//        File versionFile = Paths.get(buildDir, "..", "doc", "__version.md").toFile();
//
//        try (FileOutputStream fos = new FileOutputStream(versionFile)) {
//            try (OutputStreamWriter out = new OutputStreamWriter(fos, StandardCharsets.UTF_8)) {
//                try (BufferedWriter bw = new BufferedWriter(out)) {
//                    for (String line : lines) {
//                        bw.write(line);
//                        bw.write("\n");
//                    }
//                    String head = new String("# 文档版本信息\n\n".getBytes(), StandardCharsets.UTF_8);
//                    bw.write(head);
//                    bw.write(String.format("> build.time : %s   \n", buildTime));
//                    bw.write(String.format("> build.branch : %s   \n", buildBranch));
//                    bw.write(String.format("> build.num : %s   \n", buildNum));
//                    bw.flush();
//                }
//            }
//        }
//        System.out.println(versionFile.getAbsolutePath() + " generated!");
//    }
//
//    // private static void copy(File source, File dest, int bufferSize) {
//    // InputStream in = null;
//    // OutputStream out = null;
//    // try {
//    // in = new FileInputStream(source);
//    // out = new FileOutputStream(dest);
//    //
//    // byte[] buffer = new byte[bufferSize];
//    // int len;
//    //
//    // while ((len = in.read(buffer)) > 0) {
//    // out.write(buffer, 0, len);
//    // }
//    // } catch (Exception e) {
//    // e.printStackTrace();
//    // } finally {
//    // if (in != null) {
//    // try {
//    // in.close();
//    // } catch (Exception e) {
//    // }
//    // }
//    // if (out != null) {
//    // try {
//    // out.close();
//    // } catch (Exception e) {
//    // }
//    // }
//    // }
//    // }
//}

apiVersion: v1
kind: Namespace
metadata:
  name: skynet-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: skynet-agent
  name: skynet-agent
  namespace: skynet-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: skynet-agent
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: skynet-agent
    spec:
      containers:
      - image: ${registry.url}/${registry.context-path}/agent:${agent.project-version}-${agent.build-sid}
        imagePullPolicy: Always
        name: skynet-agent
        resources: {}
        volumeMounts:
          - name: skynet-agent-config
            mountPath: /agent.properties
            subPath: agent.properties
      volumes:
        - name: skynet-agent-config
          configMap:
            name: skynet-agent-config
      serviceAccountName: skynet-agent
status: {}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: skynet-agent-config
  namespace: skynet-system
data:
  agent.properties: |
    server.ip=${agent.ip}
    server.port=${agent.port}
    agent.project-version=${agent.project-version}
    agent.build-sid=${agent.build-sid}
    agent.build-number=${agent.build-number}
    agent.build-time=${agent.build-time}
    agent.build-branch=${agent.build-branch}
    agent.skynet-boot-version=${agent.skynet-boot-version}
    zookeeper.server-list=${zookeeper.server-list}
    zookeeper.cluster-name=${zookeeper.cluster-name}
    skynet.manager.url=${skynet.manager.url}
    skynet.home=${skynet.home}
    skynet.auth.api-key=${skynet.auth.api-key}
    skynet.auth.api-secret=${skynet.auth.api-secret}
    skynet.k8s.namespace=${skynet.k8s.namespace}
    registry.url=${registry.url}
    registry.context-path=${registry.context-path}
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: skynet-agent
  name: skynet-agent
  namespace: skynet-system
spec:
  type: NodePort
  ports:
  - port: ${agent.port}
    targetPort: ${agent.port}
    protocol: TCP
    nodePort: ${agent.port}
  selector:
    app: skynet-agent
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: skynet-agent
  namespace: skynet-system
imagePullSecrets:
  - name: skynet-docker-registry
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  creationTimestamp: null
  name: skynet-agent-role
rules:
- apiGroups:
  - ""
  resources:
  - services
  - pods
  - pods/log
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - apps
  resources:
  - deployments
  - statefulsets
  - daemonsets
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - skynet.iflytek.com
  resources:
  - skynetapps
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - skynet.iflytek.com
  resources:
  - skynetapps/finalizers
  verbs:
  - update
- apiGroups:
  - skynet.iflytek.com
  resources:
  - skynetapps/status
  verbs:
  - get
  - patch
  - update
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: skynet-agent-rolebinding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: skynet-agent-role
subjects:
- kind: ServiceAccount
  name: skynet-agent
  namespace: skynet-system

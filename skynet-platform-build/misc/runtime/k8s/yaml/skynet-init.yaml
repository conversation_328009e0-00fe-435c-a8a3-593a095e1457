apiVersion: v1
kind: Namespace
metadata:
  name: ${skynet.k8s.namespace}
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: skynet-service-account
  namespace: ${skynet.k8s.namespace}
imagePullSecrets:
  - name: skynet-docker-registry
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: skynet-init-config
  namespace: ${skynet.k8s.namespace}
data:
  init.properties: |
    skynet_manager_url=${skynet.manager.url}
    skynet_action-point=
    skynet_action_ip=${agent.ip}
    skynet_action_port=0
    skynet_action_index=0
    skynet_auth_api-key=${skynet.auth.api-key}
    skynet_auth_api-secret=${skynet.auth.api-secret}
    zookeeper.server-list=${zookeeper.server-list}
    zookeeper.cluster-name=${zookeeper.cluster-name}
    k8s_docker_hub_host=${registry.url}
    k8s_docker_hub_context-path=${registry.context-path}
    k8s_docker_hub_username=${registry.username}
    k8s_docker_hub_password=${registry.password}

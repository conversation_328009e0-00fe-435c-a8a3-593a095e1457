# -*- coding: UTF-8 -*-

#!/usr/bin/env python

from common import exec_cmd
import getopt
import sys
import time

def main():
    opts, args = getopt.getopt(sys.argv[1:], None, [])
    if(len(args) < 3):
        print '''Invalid arguments. arg1: 'cpu' or 'mem', arg2 : top N , arg3: sample duration(seconds)  '''
        exit(1)
    type = args[0]
    topN = int(args[1])
    duration = int(args[2])
    option = '%CPU' if type == 'cpu' else '%MEM'
    cmd = 'top -b -n 1 -o %s' % (option)
    pid2Sample = {}
    pid2cmd = ps()
    for i in xrange(0, duration):
        code, output = exec_cmd(cmd)
        if code != 0:
            print 'fail to execute : %s' % (cmd)
            exit(1)
        lines = toLines(output, topN)
        for line in lines:
            sample = parseLine(line)
            pid = sample['pid']
            if pid not in pid2Sample:
                pid2Sample[pid] = sample
            else:
                old = pid2Sample[pid]
                old['cpu'] = old['cpu'] + sample['cpu']
                old['mem'] = old['mem'] + sample['mem']
        time.sleep(1)

    pid2cmd = ps(pid2cmd)
    pids = pid2Sample.keys()
    keyFunc = (lambda pid: pid2Sample[pid]['cpu']) if type == 'cpu' else (lambda pid: pid2Sample[pid]['mem'])
    sortedPids = sorted(pids, key=keyFunc, reverse=True)
    print '%s %d秒采样均值 TOP %d :\n' % ('CPU' if type == 'cpu' else '内存', duration, topN)
    if type == 'cpu':
        format = '%-10s%-6s%s'
        print format % ('PID', '%CPU', 'CMD')
        for pid in sortedPids:
            sample = pid2Sample[pid]
            cmd = pid2cmd[pid] if pid in pid2cmd else '[DEAD]'
            print format % (sample['pid'], round(sample['cpu'] / duration, 2), cmd)
    else:
        format = '%-10s%-6s%-10s%-10s%s'
        print format % ('PID', '%MEM', 'VIRT', 'RES', 'CMD')
        for pid in sortedPids:
            sample = pid2Sample[pid]
            cmd = pid2cmd[pid] if pid in pid2cmd else '[DEAD]'
            print format % (sample['pid'], round(sample['mem'] / duration, 2), sample['virt'], sample['res'], cmd)




def toLines(output, topN):
    lines = output.split('\n')
    lines = map(lambda line : line.strip('\n').strip('\r').strip(' '), lines)
    lines = filter(lambda line: True if line else False, lines)
    rangeEnd = 7 + topN
    return lines[6:rangeEnd]

def parseLine(line):
    splits = line.split(' ')
    splits = filter(lambda e: True if e.strip() else False, splits)
    cpu = float(splits[8])
    mem = float(splits[9])
    return {'pid':splits[0], 'virt': splits[4], 'res': splits[5], 'cpu': cpu, 'mem': mem}


def ps(input = {}):
    code, output = exec_cmd('ps -ef')
    lines = output.split('\n')
    for line in lines:
        line = line.strip()
        if not line:
            continue
        splits = line.split()
        pid = splits[1]
        cmd = splits[7]
        input[pid] = cmd
    return input

if __name__ == "__main__":
    main()

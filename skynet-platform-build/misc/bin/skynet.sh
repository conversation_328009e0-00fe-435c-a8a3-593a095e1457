#!/bin/bash
APP=${APP:=xmanager}
#default /etc/hosts config hostname ip
IP=${IP:=127.0.0.1}
PORT=${PORT:=2230}
ISOLATION=${ISOLATION:=false}

JAVA_OPTS="-Xms512M -Xmx1G"
DEBUG_PORT=$((PORT+30000))

TimeOffset=$(date +%z)
# Skynet Home
SKYNET_HOME=$(cd $(dirname $0); cd ..; pwd)

#app config
APP_PROP=$SKYNET_HOME/conf/application.properties
SKYNET_ZK_JAAS=$SKYNET_HOME/conf/skynet.zk.jaas.cfg
SKYNET_SERVICE=$SKYNET_HOME/conf/skynet.service
SKYNET_PROP=$SKYNET_HOME/conf/skynet.properties
LIB_DIR=$SKYNET_HOME/lib

if [[ "$ISOLATION" == "true" ]];then
    PID_FILE=$SKYNET_HOME/bin/$IP/$APP.pid
    mkdir -p "$(dirname $PID_FILE)"
else
    PID_FILE=$SKYNET_HOME/bin/$APP.pid
fi

opr=$1
if [ -n "$2" ]; then
    IP=$2
fi

if [ -n "$3" ]; then
    PORT=$3
fi

CLUSTER_NAME=$(cat $SKYNET_PROP |grep 'skynet.zookeeper.cluster_name' |awk -F"=" '{print $2}'|awk '{gsub(/^\s+|\s+$/, "");print}')
if [[ -z $CLUSTER_NAME ]];then
    CLUSTER_NAME=skynet
fi

# workspace path 
JAR_FILE=$SKYNET_HOME/lib/skynet-platform-$APP-*.jar
JAR_FILE=$(echo $JAR_FILE)
SYS_SVC_NAME=$CLUSTER_NAME'_'$APP

# 新增主类变量
if [[ $APP == "xmanager" ]]; then
    MAIN_CLASS="skynet.platform.manager.Bootstrap"
elif [[ $APP == "xagent" ]]; then
    MAIN_CLASS="skynet.platform.agent.Bootstrap"
else
    MAIN_CLASS="skynet.platform.manager.Bootstrap"
fi

pid=0

export JAVA_HOME=$SKYNET_HOME/runtime/jdk-21.0.2
export CLASSPATH=.:$JAVA_HOME/lib/dt.jar:$JAVA_HOME/lib/tools.jar
export PATH=$JAVA_HOME/bin:$PATH

## check python  by lyhu@2023年11月03日15:48:59
if command -v python &> /dev/null; then
    echo "Current Python already installed, the version: "
    python --version
elif command -v python3 &> /dev/null; then
    echo "Current Python3 already installed, the version: "
    ln -s /usr/bin/python3 /usr/bin/python
    python --version
else
    echo "------------------------------------------"
    echo "Python 2 and Python 3 are not installed."
    echo "------------------------------------------"
    return
fi

fill_pid(){
    pid=$(jcmd|grep $JAR_FILE|awk '{print $1}')

    echo $pid > $PID_FILE
    if [[ ! -e $PID_FILE ]];then
        rm -f $PID_FILE
        pid=0
        return
    fi
    pid=$(cat $PID_FILE)
    if [[ -z $pid ]];then
        rm -f $PID_FILE
        pid=0
        return
    fi
    n=$(ps -ef |grep $pid|awk '{print $2}'|grep $pid|wc -l)
    if [[ n -eq 0 ]];then
        rm -f $PID_FILE
        pid=0
        return
    fi
    return 
}

system_start(){
    if [ -f $SKYNET_SERVICE ]; then
        sed -i "s|^WorkingDirectory=.*$|WorkingDirectory=$SKYNET_HOME/bin|g" $SKYNET_SERVICE
        sed -i "s|^Description=.*$|Description=$SYS_SVC_NAME|g" $SKYNET_SERVICE
        sed -i "s|^ExecStart=.*$|ExecStart=$JAVA_HOME/bin/java $CMD_LINE|g" $SKYNET_SERVICE
        sed -i "s|^Environment=JAVA_HOME=.*$|Environment=JAVA_HOME=$JAVA_HOME|g" $SKYNET_SERVICE
        sed -i "s|^Environment=CLASSPATH=.*$|Environment=CLASSPATH=$CLASSPATH|g" $SKYNET_SERVICE
        sed -i "s|^Environment=PATH=.*$|Environment=PATH=$PATH|g" $SKYNET_SERVICE
        # 异常处理
            \cp $SKYNET_SERVICE /usr/lib/systemd/system/$SYS_SVC_NAME.service >> $SKYNET_HOME/log/deploy.log 2>&1
        if [ $? -eq 0 ]; then
            systemctl daemon-reload
            #开机自启
            systemctl enable $SYS_SVC_NAME
            #启动服务
            systemctl start $SYS_SVC_NAME
        else
            return 1
        fi
    else
	    return 1
    fi
}

system_stop(){
	KillMode=process
	if [[ $1 -eq 15 ]];then
		KillMode=control-group
	fi

	if [ -f /usr/lib/systemd/system/$SYS_SVC_NAME.service ]; then
		sed -i "s|^KillMode=.*$|KillMode=$KillMode|g" /usr/lib/systemd/system/$SYS_SVC_NAME.service >> $SKYNET_HOME/log/deploy.log 2>&1
		systemctl daemon-reload
	    systemctl stop $SYS_SVC_NAME
        # remove
        rm -f /usr/lib/systemd/system/$SYS_SVC_NAME.service
    	systemctl daemon-reload
	else
		return 1
	fi			
}



start(){
    chmod -R 770 $JAVA_HOME/bin
    chmod 770 $SKYNET_HOME/runtime/k8s/*

    # 如果环境变量存在ZK地址配置（容器外传递进来），修改配置文件
    if [[ -n $ZK_HOSTS ]];then
       echo 'modify skynet.properties by sed ...'
       sed -i "s/skynet\.zookeeper\.server_list=.*/skynet\.zookeeper\.server_list=$ZK_HOSTS/g" $SKYNET_HOME/conf/skynet.properties
    fi

    fill_pid
    if [[ $pid -ne 0 ]];then
       echo "skynet-platform-$APP is running now (pid=$pid) , please stop or kill it first!" 
       exit 0
    fi

    echo ============================== Env ==================================
    echo SKYNET_HOME    =   $SKYNET_HOME
    echo JAVA_HOME      =   $JAVA_HOME
    java -version
    echo =====================================================================

    if [ $opr = 'debug' ]; then
        JAVA_OPTS="$JAVA_OPTS -Ddebug=true -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=$DEBUG_PORT"
    fi
    
    # 兼容JDK9+，不再使用-Djava.ext.dirs，改用-classpath
    SKYNET_LIB_CP="$SKYNET_HOME/lib/*"
    SKYNET_PROP_OPTS=""

    if [ -f "$SKYNET_PROP" ]; then
        #dos2unix $SKYNET_PROP
        while read line
            do
               line=$(echo $line | tr -d '\r')
               if [[ $line && ! "$line" =~ "#" ]]; then
                   SKYNET_PROP_OPTS="$SKYNET_PROP_OPTS -D$line"
               fi
        done < $SKYNET_PROP
    fi
    SKYNET_PROP_OPTS=${SKYNET_PROP_OPTS//\${SKYNET_HOME\}/$SKYNET_HOME}

    echo "try starting skynet-platform-$APP : "

    # 修改启动命令为 -cp + 主类
    CMD_LINE="$JAVA_OPTS $SKYNET_PROP_OPTS -Dskynet.home=$SKYNET_HOME -Dskynet.ipAddress=$IP -Dskynet.isolation=$ISOLATION $MAIN_CLASS --spring.config.additional-location=$APP_PROP --

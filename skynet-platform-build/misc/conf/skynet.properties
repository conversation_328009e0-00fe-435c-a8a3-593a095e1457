#-------------------------------------------------------------

# cluster_name default: skynet (If the configuration is the same as the default value, it can be commented out)
#skynet.zookeeper.cluster_name=skynet

# server_list default: 127.0.0.1:2181 (If the configuration is the same as the default value, it can be commented out)
skynet.zookeeper.server_list=127.0.0.1:2181

# session_timeout default: 20000 (If the configuration is the same as the default value, it can be commented out)
#skynet.zookeeper.session_timeout=20000

# connection_timeout default: 20000 (If the configuration is the same as the default value, it can be commented out)
#skynet.zookeeper.connection_timeout=30000

#-------------------------------------------------------------

# zookeeper Authorization SASL
#java.security.auth.login.config=${SKYNET_HOME}/conf/skynet.zk.jaas.cfg

# huawei zookeeper Authorization SASL
#zookeeper.server.principal=zookeeper/hadoop.hadoop.com
#java.security.krb5.conf=${SKYNET_HOME}/conf/huawei/krb5.conf
#java.security.auth.login.config=${SKYNET_HOME}/conf/huawei/zk.jaas.conf

#-------------------------------------------------------------

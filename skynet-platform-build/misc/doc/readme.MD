# zkServer 授权

## zookeeper server 开启权限验证
- 复制 zk_svc_conf 目录下的 jaas.cfg和java.env 到 zookeeper的/conf 目录下
- 修改其用户名和密码（jaas.cfg文件）
- 修改zoo.cfg  增加如下配置 或 直接copy 本目录下的 zoo.cfg ,注意 zk集群时需要重新修改。

```ini
sessionRequireClientSASLAuth=true
authProvider.1=org.apache.zookeeper.server.auth.SASLAuthenticationProvider
```

- 重启服务 zkServer服务


## zookeeper client 设置权限验证

- 配置文件 复制 conf/skynet.zk.jaas.cfg，在启动的命令行上增加

```
    -Djava.security.auth.login.config=skynet.zk.jaas.cfg
    
```


## 让Skynet支持 ZK授权访问设置：

1.修改 conf/skynet.zk.jaas.cfg 中的用户名和密码
2.修改启动脚本中的启动参数

将 conf/skynet.properties 中的属性注释移除
#java.security.auth.login.config=${SKYNET_HOME}/conf/skynet.zk.jaas.cfg

其他：在skynet 托管的springboot 服务自定支持 zk鉴权访问，如果不想开启，可以在 服务定义 的功能 页面禁用（禁用追加JVM参数）。





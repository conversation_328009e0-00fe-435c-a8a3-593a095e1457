package skynet.platform.common;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;
import oshi.PlatformEnum;
import oshi.SystemInfo;
import skynet.boot.SkynetProperties;
import skynet.boot.common.OsUtil;
import skynet.boot.zookeeper.SkynetZkProperties;
import skynet.platform.common.env.BootEnvironment;
import skynet.platform.common.env.BootEnvironmentBuilder;
import skynet.platform.common.logging.LogbackConfig;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.setting.SkynetSettingManager;

import java.util.LinkedHashMap;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Slf4j
public class AppBootEnvironment {

    public static final String AGENT_SERVICE_ID = "ant-xagent";
    public static final String MANAGER_SERVICE_ID = "ant-xmanager";
    public static final String ANT_CODE = "ant";
    public static final String AGENT_ACTION_POINT = AGENT_SERVICE_ID + "@" + ANT_CODE;
    public static final String MANAGER_ACTION_POINT = MANAGER_SERVICE_ID + "@" + ANT_CODE;
    public static final String DEFAULT_PD = "skynet2230";

    public static void init(String actionPoint) {
        System.out.printf("Initialize %s ...%n", actionPoint);
        System.setProperty("fastjson2.useJacksonAnnotation", "false");
        System.setProperty("skynet.token.key", "skynet_token");

        Assert.isTrue(SystemInfo.getCurrentPlatform() != PlatformEnum.UNKNOWN, "Not Support Current Platform.");

        log.debug("Init actionPoint = {} begin ...", actionPoint);
        MDC.put("actionId", actionPoint);
        //提前设置一个默认的，目的连接ZK过程中 就会生成一些日志，
        System.setProperty("logging.file", String.format("../log/%s.log", actionPoint));
        System.setProperty("logging.file.name", String.format("../log/%s.log", actionPoint));

        System.setProperty(SkynetProperties.SKYNET_ACTION_POINT_KEY, actionPoint);

        System.setProperty("spring.config.name", String.format("application%s", actionPoint)
                .replaceAll("ant", "").replace("@", ""));

        System.setProperty("skynet.pid", String.valueOf(OsUtil.getCurrentPid()));
        System.setProperty("encrypt.key-store.pass" + "word", DEFAULT_PD);

        System.setProperty("file.encoding", System.getProperty("file.encoding", "UTF-8"));
//        System.setProperty("user.timezone", System.getProperty("user.timezone", "GMT+0800"));

        log.debug("Init actionPoint={} end.", actionPoint);
    }

    public static BootEnvironment getBootEnvironment(Environment environment, IAntConfigService antConfigService, SkynetProperties skynetProperties) throws Exception {

        log.debug("getBootEnvironment skynetProperties={} begin...", skynetProperties);
        SkynetZkProperties skynetZkProperties = antConfigService.getSkynetZKProperties();
        String actionPoint = skynetProperties.getActionPoint();

        System.setProperty(SkynetZkProperties.SKYNET_ZOOKEEPER_ENABLED, String.valueOf(StringUtils.isNoneBlank(skynetZkProperties.getServerList())).toLowerCase());

        SkynetSettingManager skynetSettingManager = new SkynetSettingManager(antConfigService);
        //移除 zk ant 插件的配置，防止 业务配置错误 影响 托管平台的启动。
//        Map<String, Object> map = skynetSettingManager.getPluginConfigProperties(ANT_CODE);
//        //移除所有 skynet.security 属性，防止影响了 skynet托管平台的安全配置
//        map.entrySet().removeIf(entry -> entry.getKey().startsWith("skynet.security"));
//        map.entrySet().removeIf(entry -> entry.getKey().startsWith("spring.security"));
//        map.entrySet().removeIf(entry -> entry.getKey().startsWith("skynet.tlb"));
        Map<String, Object> map = new LinkedHashMap<>();
        map.put("logging.pattern.console", map.getOrDefault("logging.pattern.console", LogbackConfig.getLoggingPatternConsole()));
        map.put("logging.pattern.file", map.getOrDefault("logging.pattern.file", LogbackConfig.getLoggingPatternFile()));
        //不容许 外界修改 xagent的日志文件名，只许修改日志路径 by lyhu
        map.put("logging.file.name", LogbackConfig.getLogFullFileName(actionPoint, map));
        map.put("spring.application.name", skynetProperties.getActionCode());
        map.put("spring.cloud.zookeeper.connect-string", skynetZkProperties.getServerList());
        map.put("spring.cloud.zookeeper.discovery.root", String.format("/%s/discovery", skynetZkProperties.getClusterName()));
        map.put("spring.cloud.zookeeper.discovery.instance-host", map.getOrDefault("spring.cloud.zookeeper.discovery.instance-host", skynetProperties.getIpAddress()));

        map.put("encrypt.key-store.pass" + "word", map.getOrDefault("encrypt.key-store.pass" + "word", DEFAULT_PD));

        map.put("skynet.security.base-auth.user.name", map.getOrDefault("skynet.security.base-auth.user.name", "admin"));
        map.put("skynet.security.base-auth.user.pass" + "word", map.getOrDefault("skynet.security.base-auth.user.pass" + "word", DEFAULT_PD));

        map.put(SkynetProperties.SKYNET_ACTION_POINT_KEY, actionPoint);
        map.put(SkynetProperties.SKYNET_ACTION_ID_KEY, actionPoint);
        map.put(SkynetProperties.SKYNET_ACTION_TITLE_KEY, actionPoint);
        map.put(SkynetProperties.SKYNET_ACTION_DESC_KEY, actionPoint);

        log.info("Load {} Property from ZK end.", actionPoint);
        Map<String, Object> skynetEnvMap = BootEnvironmentBuilder.getSkynetEnvironment(skynetZkProperties, skynetProperties);
        BootEnvironment bootEnvironment = new BootEnvironment(environment);
        bootEnvironment.putAll(skynetEnvMap);
        bootEnvironment.putAll(map);
        log.debug("getBootEnvironment end.");

        return bootEnvironment;
    }
}

package skynet.platform.common.prometheus;

import org.apache.commons.lang3.StringUtils;
import skynet.platform.common.repository.config.IAntConfigService;

import java.util.Map;

public class PrometheusUtils {

    public final static String EXCLUSION_METRICS_KEY = "prometheus.exclusion.metrics";

    public static String getExclusionMetrics(IAntConfigService antConfigService) {

        String path = String.format("/%s/mon/metrics_exclusion", antConfigService.getClusterName());
        Map<String, String> metricsMap = antConfigService.getChildrenWithData2(path, null);

        return StringUtils.join(metricsMap.keySet(), ";");
    }

}

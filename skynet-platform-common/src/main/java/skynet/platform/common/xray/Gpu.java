package skynet.platform.common.xray;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.plexus.util.cli.CommandLineException;
import org.codehaus.plexus.util.cli.Commandline;
import skynet.boot.common.UtilAll;
import skynet.platform.common.utils.cmd.CommandLineExecutor;
import skynet.platform.common.xray.domain.NvidiaGPU;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Gpu 性能指标采集器
 *
 * <AUTHOR>
 */
@Slf4j
public class Gpu {

    /**
     * nvidia-smi 指令
     */
    private static final String NVIDIA_QUERY_COMMAND = "nvidia-smi --query-gpu=%s --format=csv,nounits,noheader";

    /**
     * nvidia-smi 默认查询项
     */
    private static final String NVIDIA_QUERY_DEFAULT_ITEM = "index,name,uuid,serial,utilization.gpu,utilization.memory,memory.total,memory.free,memory.used,power.draw,power.limit,fan.speed,temperature.gpu,compute_mode,clocks.current.graphics,clocks.current.sm,clocks.current.memory";

    private static volatile Class<?> lastErrorType = null;

    private volatile int gpuCount = -1;
    private final Commandline commandline;
    private final String queryItemString;

    public Gpu() {
        this(NVIDIA_QUERY_DEFAULT_ITEM);
    }

    public Gpu(List<String> queryItemList) {
        this(StringUtils.join(queryItemList, ","));
    }

    public Gpu(String queryItemString) {
        if (StringUtils.isBlank(queryItemString)) {
            queryItemString = NVIDIA_QUERY_DEFAULT_ITEM;
        }
        String nvidiaQueryCmd = String.format(NVIDIA_QUERY_COMMAND, queryItemString);
        this.commandline = new Commandline(nvidiaQueryCmd);
        this.queryItemString = queryItemString;

    }

    public List<Map<String, Object>> getMetrics() {
        // 尝试初始化gpu数量
        if (this.gpuCount < 0) {
            synchronized (this) {
                if (this.gpuCount < 0) {
                    this.gpuCount = getGraphicsCard().size();
                }
            }
        }
        // 如果存在gpu，采集信息
        if (this.gpuCount > 0) {
            try {
                List<String> lines = CommandLineExecutor.execute(this.commandline);
                return parseResult(queryItemString, lines);
            } catch (CommandLineException e) {
                log.error("execute commandline error." + e.getMessage());
            } catch (Exception e) {
                if (e.getClass() != lastErrorType) {
                    log.error("execute commandline error.", e);
                    lastErrorType = e.getClass();
                }
            }
        }

        return new ArrayList<>(0);
    }

    private static List<NvidiaGPU> lastGraphicsCardInfos;

    /**
     * 获取机器显卡个数
     * <p>
     * 有显卡返回 list，list 里存储显卡简单信息 没有显卡 list size 为 0
     */
    public static List<NvidiaGPU> getGraphicsCard() {

        // lastGraphicsCardInfos.size() ==0 表示没有显卡
        if (lastGraphicsCardInfos == null || !lastGraphicsCardInfos.isEmpty()) {
            List<NvidiaGPU> graphicsCardInfos = new ArrayList<>();
            try {
                String nvidiaQueryCmd = String.format(NVIDIA_QUERY_COMMAND, NVIDIA_QUERY_DEFAULT_ITEM);
                Commandline commandline = new Commandline(nvidiaQueryCmd);
                List<String> lines = CommandLineExecutor.execute(commandline);
                List<Map<String, Object>> objList = parseResult(NVIDIA_QUERY_DEFAULT_ITEM, lines);

                for (Map<String, Object> map : objList) {
                    NvidiaGPU nvidiaGPU = JSON.parseObject(JSON.toJSONString(map), NvidiaGPU.class);
                    graphicsCardInfos.add(nvidiaGPU);
                }
            } catch (Exception e) {
                if (e.getClass() != lastErrorType) {
                    if (e instanceof CommandLineException) {
                        log.warn("Gpu.getGraphicsCard Error:\t{}", e.getMessage());
                    } else {
                        log.error("execute commandline error.", e);
                    }
                    lastErrorType = e.getClass();
                }
            }

            lastGraphicsCardInfos = graphicsCardInfos;
        }
        return lastGraphicsCardInfos;
    }


    private static List<Map<String, Object>> parseResult(String queryItemString, List<String> gpuList) {

        List<Map<String, Object>> objList = new ArrayList<>();
        if (gpuList.isEmpty()) {
            return objList;
        }
        String[] keys = queryItemString.split(",");
        for (String line : gpuList) {
            String[] itemValues = line.split(",");
            Map<String, Object> map = new HashMap<>(itemValues.length);
            for (int i = 0; i < itemValues.length; ++i) {
                String itemValue = itemValues[i].trim();
                if (UtilAll.isInt(itemValue)) {
                    map.put(keys[i], Long.parseLong(itemValue));
                } else if (UtilAll.isFloat(itemValue)) {
                    map.put(keys[i], Float.parseFloat(itemValue));
                } else {
                    map.put(keys[i], itemValue);
                }
            }
            objList.add(map);
        }
        return objList;
    }

    public static void main(String[] args) {
        // 使用样例

        // 获取 gpu 卡信息
        List<NvidiaGPU> graphicsCardInfos = Gpu.getGraphicsCard();
        log.info("card number ={}", graphicsCardInfos.size());

        List<String> queryItem = new ArrayList<>();
        queryItem.add("timestamp,index,name");
        queryItem.add("serial,utilization.gpu");
        queryItem.add("memory.total");

        // 查找输入字段
        Gpu gpu = new Gpu(queryItem);
        System.out.println(JSON.toJSONString(gpu.getMetrics(), JSONWriter.Feature.PrettyFormat));

        // 默认查找全部字段
        gpu = new Gpu();
        System.out.println(JSON.toJSONString(gpu.getMetrics(), JSONWriter.Feature.PrettyFormat));
    }
}

package skynet.platform.common.xray.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import oshi.software.os.OSFileStore;
import skynet.platform.common.xray.annotation.PrometheusMetric;
import skynet.platform.common.xray.annotation.PrometheusMetricLabel;

@Getter
@Setter
@Accessors(chain = true)
public class DiskStat extends PerfStatBase {

    public static final String METRIC_TYPE = "x-disk";

    @PrometheusMetricLabel("dev")
    private String devName;

    @PrometheusMetricLabel("mountpoint")
    private String dirName;

    @PrometheusMetricLabel("fstype")
    private String fsType;

    @PrometheusMetric("xray_disk_capacity_total")
    private long capacityTotal;

    @PrometheusMetric("xray_disk_capacity_free")
    private long capacityFree;

    @PrometheusMetric("xray_disk_capacity_used")
    private long capacityUsed;

//    @PrometheusMetric("xray_disk_capacity_avail")
//    private long capacityAvail;

    @PrometheusMetric("xray_disk_capacity_used_perc")
    private double capacityUsePercent;

//    @PrometheusMetric("xray_disk_files")
//    private long files;
//
//    @PrometheusMetric("xray_disk_free_files")
//    private long freeFiles;
//
//    @PrometheusMetric("xray_disk_reads")
//    private long reads;
//
//    @PrometheusMetric("xray_disk_writes")
//    private long writes;
//
//    @PrometheusMetric("xray_disk_read_bytes")
//    private long readBytes;
//
//    @PrometheusMetric("xray_disk_write_bytes")
//    private long writeBytes;
//
//    @PrometheusMetric("xray_disk_queue")
//    private double queue;
//
//    @PrometheusMetric("xray_disk_srv_time")
//    private double serviceTime;
//
//    @PrometheusMetric("xray_disk_r_per_sec")
//    private long readPerSec;
//
//    @PrometheusMetric("xray_disk_w_per_sec")
//    private long writePerSec;
//
//    @PrometheusMetric("xray_disk_r_bytes_per_sec")
//    private long readBytesPerSec;
//
//    @PrometheusMetric("xray_disk_w_bytes_per_sec")
//    private long writeBytesPerSec;

    public DiskStat() {
        super(METRIC_TYPE);
    }

//    public DiskStat(FileSystem info, FileSystemUsage lastUsage, FileSystemUsage currentUsage, long intervalMs) {
//        super(METRIC_TYPE);
//        this.devName = info.getDevName();
//        this.dirName = info.getDirName();
//        this.fsType = info.getSysTypeName();
//        this.capacityTotal = currentUsage.getTotal() * 1024L;
//        this.capacityFree = currentUsage.getFree() * 1024L;
//        this.capacityUsed = currentUsage.getUsed() * 1024L;
//        this.capacityAvail = currentUsage.getAvail() * 1024L;
//        this.capacityUsePercent = currentUsage.getUsePercent();
//        this.files = currentUsage.getFiles();
//        this.freeFiles = currentUsage.getFreeFiles();
//        this.reads = currentUsage.getDiskReads();
//        this.writes = currentUsage.getDiskWrites();
//        this.readBytes = currentUsage.getDiskReadBytes();
//        this.writeBytes = currentUsage.getDiskWriteBytes();
//        this.queue = currentUsage.getDiskQueue();
//        this.serviceTime = currentUsage.getDiskServiceTime();
//        long intervalSec = intervalMs / 1000;
//        if (lastUsage != null && intervalSec > 0) {
//            this.readPerSec = (this.reads - lastUsage.getDiskReads()) / intervalSec;
//            this.writePerSec = (this.writes - lastUsage.getDiskWrites()) / intervalSec;
//            this.readBytesPerSec = (this.readBytes - lastUsage.getDiskReadBytes()) / intervalSec;
//            this.writeBytesPerSec = (this.writeBytes - lastUsage.getDiskWriteBytes()) / intervalSec;
//        }
//    }

    public DiskStat(OSFileStore osFileStore) {
        super(METRIC_TYPE);
        this.devName = osFileStore.getName();
        this.dirName = osFileStore.getMount();
        this.fsType = osFileStore.getType();

        this.capacityTotal = osFileStore.getTotalSpace();
        this.capacityFree = osFileStore.getFreeSpace();
        this.capacityUsed = this.capacityTotal - this.capacityFree;//  osFileStore.getUsableSpace();
        this.capacityUsePercent = (capacityTotal == 0) ? 0.0 : PerfStatBase.format(capacityUsed / (capacityTotal * 1.0f));
    }

    @Override
    public String toString() {
        return super.toString();
    }


}

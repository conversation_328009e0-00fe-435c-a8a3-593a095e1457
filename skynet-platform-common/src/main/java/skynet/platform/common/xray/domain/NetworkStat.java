package skynet.platform.common.xray.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import oshi.hardware.NetworkIF;
import skynet.platform.common.xray.annotation.PrometheusMetric;
import skynet.platform.common.xray.annotation.PrometheusMetricLabel;

@Getter
@Setter
@Accessors(chain = true)
public class NetworkStat extends PerfStatBase {

    public static final String METRIC_TYPE = "x-net";

    @PrometheusMetricLabel("interface")
    private String devName;

    @PrometheusMetricLabel("addr")
    private String address;

    @PrometheusMetricLabel("ipv6")
    private String ipv6;

    private String hwAddress;

    @PrometheusMetric("xray_net_rx_bytesps")
    private long rxSpeed;

    @PrometheusMetric("xray_net_tx_bytesps")
    private long txSpeed;

    public NetworkStat() {
        super(METRIC_TYPE);
    }

//    public NetworkStat(NetInterfaceConfig ifConfig, NetInterfaceStat lastNetStat, NetInterfaceStat currentNetStat, long intervalMs) {
//        super(METRIC_TYPE);
//        this.devName = ifConfig.getName();
//        this.address = ifConfig.getAddress();
//        this.hwAddress = ifConfig.getHwaddr();
//        long intervalSec = intervalMs / 1000;
//        if (lastNetStat != null && currentNetStat != null && intervalSec > 0) {
//            this.rxSpeed = (currentNetStat.getRxBytes() - lastNetStat.getRxBytes()) / intervalSec;
//            this.txSpeed = (currentNetStat.getTxBytes() - lastNetStat.getTxBytes()) / intervalSec;
//        }
//    }

    public NetworkStat(NetworkIF networkIF, NetworkIF lastNetworkIF, long intervalMs) {
        super(METRIC_TYPE);

        this.devName = networkIF.getName();
        if (networkIF.getIPv4addr().length > 0) {
            this.address = networkIF.getIPv4addr()[0];
        }
        if (networkIF.getIPv6addr().length > 0) {
            this.ipv6 = networkIF.getIPv6addr()[0];
        }
        this.hwAddress = networkIF.getMacaddr();

        //获取发送速度
        long intervalSec = intervalMs / 1000;
        if (lastNetworkIF != null && intervalSec > 0) {
            this.rxSpeed = (networkIF.getBytesRecv() - lastNetworkIF.getBytesRecv()) / intervalSec;
            this.txSpeed = (networkIF.getBytesSent() - lastNetworkIF.getBytesSent()) / intervalSec;
        }
    }

    @Override
    public String toString() {
        return super.toString();
    }

}

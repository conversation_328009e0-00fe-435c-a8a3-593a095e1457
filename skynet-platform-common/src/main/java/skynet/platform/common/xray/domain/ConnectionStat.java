package skynet.platform.common.xray.domain;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import oshi.software.os.InternetProtocolStats;
import skynet.platform.common.xray.annotation.PrometheusMetric;

import java.util.List;

@Getter
@Setter
@Accessors(chain = true)
public class ConnectionStat extends PerfStatBase {

    public static final String METRIC_TYPE = "x-connection";

    @PrometheusMetric("xray_tcp_listen")
    private long tcpListen;

    @PrometheusMetric("xray_tcp_established")
    private long tcpEstablised;

    @PrometheusMetric("xray_tcp_time_wait")
    private long tcpTimeWait;

    @PrometheusMetric("xray_tcp_close_wait")
    private long tcpCloseWait;

    @PrometheusMetric("xray_tcp_last_ack")
    private long tcpLastAck;

    @PrometheusMetric("xray_tcp_syn_sent")
    private long tcpSynSent;

    // add 3.4.0
    @PrometheusMetric("xray_tcp_total")
    private long tcpTotal;

//    @PrometheusMetric("xray_tcp_inbound_total")
//    private long tcpInboundTotal;
//
//    @PrometheusMetric("xray_tcp_outbound_total")
//    private long tcpOutboundTotal;

//    @PrometheusMetric("xray_connection_inbound_total")
//    private long allInboundTotal;
//
//    @PrometheusMetric("xray_connection_outbound_total")
//    private long allOutboundTotal;


    public ConnectionStat() {
        super(METRIC_TYPE);
    }

    public ConnectionStat(InternetProtocolStats stats) {
        super(METRIC_TYPE);

        List<InternetProtocolStats.IPConnection> connections = stats.getConnections();
        this.tcpListen = connections.stream().filter(x -> x.getState() == InternetProtocolStats.TcpState.LISTEN).count();
        this.tcpEstablised = connections.stream().filter(x -> x.getState() == InternetProtocolStats.TcpState.ESTABLISHED).count();
        this.tcpTimeWait = connections.stream().filter(x -> x.getState() == InternetProtocolStats.TcpState.TIME_WAIT).count();
        this.tcpCloseWait = connections.stream().filter(x -> x.getState() == InternetProtocolStats.TcpState.CLOSE_WAIT).count();
        this.tcpLastAck = connections.stream().filter(x -> x.getState() == InternetProtocolStats.TcpState.LAST_ACK).count();
        this.tcpSynSent = connections.stream().filter(x -> x.getState() == InternetProtocolStats.TcpState.SYN_SENT).count();

        this.tcpTotal = connections.size();
    }

    @Override
    public String toString() {
        return super.toString();
    }

}

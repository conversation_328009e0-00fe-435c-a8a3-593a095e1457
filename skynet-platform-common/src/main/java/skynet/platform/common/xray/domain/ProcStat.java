package skynet.platform.common.xray.domain;

import lombok.Getter;
import oshi.software.os.OSProcess;
import skynet.platform.common.xray.annotation.PrometheusMetric;
import skynet.platform.common.xray.annotation.PrometheusMetricLabel;

@Getter
public class ProcStat extends PerfStatBase {

    public static final String METRIC_TYPE = "x-proc";

    @PrometheusMetricLabel("aid")
    private String aid;

    @PrometheusMetricLabel("ip")
    private String ip;

    @PrometheusMetricLabel("port")
    private int port;

    @PrometheusMetricLabel("pid")
    private long pid;

    private long ppid;

    /**
     * The name.
     */
    @PrometheusMetricLabel("name")
    private String name;

    /**
     * The cpu percent.
     */
    @PrometheusMetric("xray_proc_cpu_perc_of_all")
    private double cpuPercentOfAllCores;

    /**
     * The cpu percent.
     */
    @PrometheusMetric("xray_proc_cpu_perc_of_single")
    private double cpuPercentOfSingleCore;

    /**
     * The mem resident.
     */
    @PrometheusMetric("xray_proc_mem_resident")
    private long memResident;

    /**
     * The mem shared.
     */
    @PrometheusMetric("xray_proc_mem_shared")
    private long memShared;

    /**
     * The mem virtual.
     */
    @PrometheusMetric("xray_proc_mem_virtual")
    private long memVirtual;

    /**
     * The threads.
     */
    @PrometheusMetric("xray_proc_threads")
    private long threads;

    /**
     * files opened total
     **/
    @PrometheusMetric("xray_proc_fd_total")
    private long fdTotal;

//    public ProcStat(String aid, String ip, int port, long pid, ProcState state, ProcCpu cpu, ProcMem mem, ProcFd procFd) {
//        super(METRIC_TYPE);
//        this.aid = aid;
//        this.ip = ip;
//        this.port = port;
//        this.pid = pid;
//        this.ppid = state.getPpid();
//        this.name = state.getName();
//        this.cpuPercentOfSingleCore = cpu.getPercent();
//        this.cpuPercentOfAllCores = new BigDecimal(cpuPercentOfSingleCore / (Cpu.CORES * 1.0)).setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
//        this.memResident = mem.getResident();
//        this.memShared = mem.getShare();
//        this.memVirtual = mem.getSize();
//        this.threads = state.getThreads();
//        this.fdTotal = procFd == null ? 0 : procFd.getTotal();
//    }


    public ProcStat(String aid, String ip, int port, long pid, OSProcess p) {
        super(METRIC_TYPE);
        this.aid = aid;
        this.ip = ip;
        this.port = port;
        this.pid = pid;
        if (p != null) {
            //获取此进程父进程的进程ID
            this.ppid = p.getParentProcessID();
            //获取进程的名称
            this.name = p.getName();

            //获取cpu使用率
            this.cpuPercentOfSingleCore = 0;

            //总体：cpu使用率
            this.cpuPercentOfAllCores = PerfStatBase.format(p.getProcessCpuLoadCumulative());

            //常驻内存
            this.memResident = p.getResidentSetSize();
            //TODO:共享内存
            this.memShared = 0;
            //虚拟内存
            this.memVirtual = p.getVirtualSize();
            this.threads = p.getThreadCount();
            this.fdTotal = p.getOpenFiles();
        }
    }
}

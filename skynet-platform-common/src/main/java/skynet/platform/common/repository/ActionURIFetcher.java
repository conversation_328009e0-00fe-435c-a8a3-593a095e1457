package skynet.platform.common.repository;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;
import skynet.boot.common.OsUtil;
import skynet.boot.common.domain.Jsonable;
import skynet.platform.common.exception.AntException;
import skynet.platform.common.repository.domain.AntActionStatus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * ActionURI 获取器
 *
 * <pre>
 * 优先 获取在线服务的配置地址
 * 使用示例：
 * &#64;Configuration
 * class ActionURIFetcherConfig {
 *
 * 	&#64;Bean
 *  &#64;Qualifier("secondaryConfig") //如果多个配置属性的时候使用
 * 	&#64;ConfigurationProperties(prefix = "skynet.proc.dispatch")
 * 	public ActionURIFetcher getActionURIFetcher(OnlineActionManager onlineActionManager) {
 * 		return new ActionURIFetcher(onlineActionManager);
 *    }
 * }
 *
 * #使用的时候
 * &#64;Qualifier("secondaryConfig") ActionURIFetcher actionURIFetcher
 *
 * 配置示例：
 * skynet.proc.dispatch.actionName=proc-dispatch@sample
 * skynet.proc.dispatch.actionUrlList=http://127.0.0.1:7000,http://127.0.0.1:7001
 * </pre>
 *
 * <AUTHOR> [2019年2月18日 下午5:08:56]
 */
@Slf4j
public class ActionURIFetcher extends Jsonable {

    private final OnlineActionManager onlineActionManager;

    @Getter
    @Setter
    private String actionName;
    @Getter
    @Setter
    private String actionUrlList;

    private List<String> tempActionUrlList;

    private List<String> getConfigActionUrlList() {
        if (tempActionUrlList == null) {
            tempActionUrlList = (StringUtils.isNoneBlank(actionUrlList)) ? Arrays.asList(StringUtils.split(actionUrlList.replaceAll(",", ";").replaceAll("，", ";").replaceAll("；", ";"), ";"))
                    : new ArrayList<>(0);
        }

        return tempActionUrlList;
    }

    public ActionURIFetcher(OnlineActionManager onlineActionManager) {
        this.onlineActionManager = onlineActionManager;
    }

    /**
     * 获取一个 WebURL地址
     *
     * <pre>
     * </pre>
     *
     * @param isFirstLocalIP 是否是本机优先
     * @return
     */
    public String getWebUrl(boolean isFirstLocalIP) {
        Assert.hasText(actionName, "the xxx.actionName is not config or is blank.");

        AntActionStatus antActionStatus = onlineActionManager.getNode(actionName, isFirstLocalIP);
        log.debug("Online actionName:[{}], antActionStatus:{} ", actionName, antActionStatus);

        String url = null;
        try {

            if (antActionStatus != null) {
                url = antActionStatus.getRestWebUri();
                return url;
            }

            List<String> configList = getConfigActionUrlList();
            if (configList.isEmpty()) {
                log.error("Not online action:{} or actionUrlList is null", actionName);
                throw new AntException("Not online action:{} or actionUrlList is null", actionName);
            }

            List<String> localList = new ArrayList<>();

            if (isFirstLocalIP) {
                List<String> ips = OsUtil.getAllIPs();
                for (String ip : ips) {
                    localList.addAll(configList.stream().filter(x -> x.contains(ip)).collect(Collectors.toList()));
                }
                localList = localList.size() == 0 ? configList : localList;
            } else {
                localList.addAll(configList);
            }

            url = localList.get(new Random().nextInt(localList.size()));
            return url;
        } finally {
            log.debug("return web uri: {} ", url);
        }

    }

    /**
     * 获取所有的 WebURL地址列表
     *
     * <pre>
     * </pre>
     *
     * @return
     */
    @JSONField(serialize = false)
    public List<String> getAllWebUrlList() throws Exception {
        Assert.hasText(actionName, "the xxx.actionName is not config or is blank.");

        List<AntActionStatus> antActionStatusList = onlineActionManager.getAllNodes(actionName);
        log.debug("Online actionName=[{}], size={} ", actionName, antActionStatusList.size());

        if (antActionStatusList.size() == 0) {
            return antActionStatusList.stream().map(AntActionStatus::getRestWebUri).collect(Collectors.toList());
        }

        // 获取 直接配置的
        List<String> configList = getConfigActionUrlList();
        if (configList.isEmpty()) {
            log.error("Not online action={} or actionUrlList is null", actionName);
            throw new AntException("not online action={} or actionUrlList is null", actionName);
        }

        return configList;
    }
}
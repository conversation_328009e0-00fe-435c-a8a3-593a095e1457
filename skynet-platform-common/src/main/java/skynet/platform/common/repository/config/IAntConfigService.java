/**
 *
 */
package skynet.platform.common.repository.config;


/**
 * Skynet 配置服务接口契约
 *
 * <AUTHOR>
 */
public interface IAntConfigService extends

        AntConfigService4Base,

        AntConfigService4Status,

        AntConfigService4Plugin,

        AntConfigService4Setting,

        AntConfigService4Properties,

        AntConfigService4Logger,

        AntConfigService4Server,
        AntConfigService4Menu,
        AutoCloseable {

}

package skynet.platform.common.repository.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import skynet.boot.common.domain.Jsonable;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Slf4j
@Getter
@Setter
@Accessors(chain = true)
public class AntActionStatus extends Jsonable {

    @JSONField(ordinal = 10)
    private String action;

    @JSONField(ordinal = 12)
    private String name;

    @JSONField(ordinal = 14)
    private String type = "server";

    @JSONField(ordinal = 15)
    private String protocol = "http";

    @JSONField(ordinal = 20)
    private String ip;

    @JSONField(ordinal = 30)
    private int port;

    @JSONField(ordinal = 31)
    private List<Integer> ports = new ArrayList<>(0);

    @JSONField(ordinal = 35)
    private String path;

    @JSONField(ordinal = 40)
    private long pid;

    /**
     * 扩展 标签
     */
    @JSONField(ordinal = 48)
    private List<AntActionLabel> actionLabels = new ArrayList<>(0);

    @JSONField(ordinal = 50, format = "yyyy-MM-dd HH:mm:ss")
    private Date start;

    @JSONField(ordinal = 55, format = "yyyy-MM-dd HH:mm:ss")
    private Date report;

    @JSONField(ordinal = 60)
    private Map<String, Object> metadata;

    @JSONField(ordinal = 70)
    private String from;

    public AntActionStatus() {
        this.start = new Date();
        this.report = this.start;
    }

    @JSONField(serialize = false)
    public String getNodeName() {
        return String.format("%s:%d", ip, port);
    }

    /**
     * 通过code 获取服务标签
     *
     * @param code
     * @return
     */
    public AntActionLabel getActionLabelByCode(String code) {
        if (StringUtils.isBlank(code)) {
            throw new IllegalArgumentException("code is blank");
        }
        if (actionLabels != null) {
            for (AntActionLabel antActionLabel : actionLabels) {
                if (code.equals(antActionLabel.getCode())) {
                    log.debug("extended attribute：{}", antActionLabel);
                    return antActionLabel;
                }
            }
        }
        return null;
    }


    /**
     * 获取 Rest Web Uri
     * <p>
     * http://**************:9090
     *
     * @return
     */
    @JSONField(serialize = false)
    public String getRestWebUri() {
        return String.format("%s://%s:%d", protocol, this.ip, this.port);
    }

    @Override
    public String toString() {
        return super.toString();
    }

}

package skynet.platform.common.repository.config.observer;

import com.alibaba.fastjson2.JSON;
import org.apache.commons.lang3.StringUtils;
import skynet.platform.common.domain.AntServerParam;

import java.util.Observable;
import java.util.Observer;

/**
 * ZK中的 Ant服务器中的AntWorker 变化观察器
 *
 * <AUTHOR>
 */
public class ServerParamObserver implements Observer {

    public void update(AntServerParam antServerParam) {
    }

    @Override
    public void update(Observable o, Object arg) {

        AntServerParam antWorkerParams = null;
        String map = (String) arg;
        if (!StringUtils.isBlank(map)) {
            antWorkerParams = JSON.parseObject(map, AntServerParam.class);
        }

        update(antWorkerParams);
    }
}

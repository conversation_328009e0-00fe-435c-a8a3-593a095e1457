package skynet.platform.common.repository.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.DigestUtils;
import skynet.boot.common.domain.Jsonable;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 首页菜单
 * </p>
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class AntMenuView extends Jsonable implements Comparable<AntMenuView> {

    @JSONField(ordinal = 10)
    private int index;

    @JSONField(ordinal = 20)
    private String mid;

    @JSONField(ordinal = 30)
    private String name;

    @JSONField(ordinal = 40)
    private String icon;

    @JSONField(ordinal = 50)
    private boolean show = true;

    @JSONField(ordinal = 60)
    private String url;

    @JSONField(ordinal = 65)
    private String context;

    @J<PERSON>NField(ordinal = 70)
    private List<AntMenuView> menus;

    public AntMenuView() {
        this(null);
    }

    public AntMenuView(String name) {
        this(name, "about:blank");
    }

    public AntMenuView(String name, String url) {
        this.mid = "MENU" + RandomStringUtils.randomAlphanumeric(12).toUpperCase();
        this.name = name;
        this.url = url;
        this.menus = new ArrayList<>();
    }

    @Override
    public int compareTo(AntMenuView o) {
        return (this.index - o.index);
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof AntMenuView) {
            AntMenuView s = (AntMenuView) obj;
            return this.name.trim().equals(s.name.trim());
        }
        return super.equals(obj);
    }

    @JSONField(serialize = false)
    public String getMenuMd5() {
        List<String> midList = new ArrayList<>();
        midList.add(mid);
        for (AntMenuView item : this.menus) {
            midList.add(item.getMid());
        }
        Collections.sort(midList);
        return DigestUtils.md5DigestAsHex(StringUtils.join(midList, "-").getBytes(StandardCharsets.UTF_8)).substring(20);
    }

    @Override
    public String toString() {
        return super.toString();
    }
}

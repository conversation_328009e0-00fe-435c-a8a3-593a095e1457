package skynet.platform.common.shell;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.FileSystemUtils;
import skynet.platform.common.utils.cmd.CommandLineExecutor;

import java.io.File;
import java.io.FileNotFoundException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * 操作本地 shell 类
 *
 * <AUTHOR>
 */

@Slf4j
class Shell4Local implements Shell {

    @Override
    public String execCmd(String... commands) {
        String cmd = StringUtils.join(commands, " && ");
        log.debug("exec cmd-[{}]", cmd);
        String result = CommandLineExecutor.executeShell(cmd);
        log.debug("ReturnResult={}", result);
        return result;
    }

    @Override
    public boolean copy(File srcFile, String absoluteDestDir) throws Exception {
        if (srcFile == null || absoluteDestDir == null) {
            throw new NullPointerException("srcFile or dst is null");
        }
        if (!srcFile.exists()) {
            throw new FileNotFoundException(String.format("srcFile doesn't exist. srcFile.path=%s", srcFile.getAbsolutePath()));
        }
        mkdir(absoluteDestDir);
        Path dest = Paths.get(String.format("%s%s%s", absoluteDestDir, File.separator, srcFile.getName()));
        if (srcFile.isDirectory()) {
            FileUtils.copyDirectory(srcFile, dest.toFile());
            return true;
        }
        FileSystemUtils.copyRecursively(srcFile, dest.toFile());
        // FileUtils.copyFile(srcFile, dest.toFile());
        return true;
    }

    @Override
    public boolean isFileExist(String absoluteFilePath) throws Exception {
        return exists(absoluteFilePath);
    }

    @Override
    public boolean isDirExist(String absoluteDirPath) throws Exception {
        return exists(absoluteDirPath);
    }

    @Override
    public boolean mkdir(String absoluteDirPath) throws Exception {
        File file = new File(absoluteDirPath);
        if (file.isDirectory()) {
            return true;
        }

        return file.mkdirs();
    }

    @Override
    public String readFile(String absoluteFilePath) throws Exception {
        if (StringUtils.isBlank(absoluteFilePath)) {
            throw new Exception("file path is blank");
        }

        File file = new File(absoluteFilePath);
        if (file.isDirectory()) {
            throw new Exception(String.format("file is a directory. filePath=%s", absoluteFilePath));
        }

        return FileUtils.readFileToString(file, StandardCharsets.UTF_8);
    }

    private boolean exists(String path) {
        File file = new File(path);
        return file.exists();
    }

    @Override
    public void close() throws Exception {

    }
}

package skynet.platform.common.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

/**
 * 服务器登录信息
 *
 * <pre>
 * </pre>
 *
 * <AUTHOR> [Oct 19, 2017 2:17:05 PM]
 */
@Getter
@Setter
public class ServerLoginParam extends Jsonable {

    @JsonProperty(index = 10)
    @JSONField(ordinal = 10)
    private String ip;

    @JsonProperty(index = 20)
    @JSONField(ordinal = 20)
    private int port = 22;

    @JsonProperty(index = 30)
    @JSONField(ordinal = 30)
    private String user = "root";

    @JsonProperty(index = 40)
    @JSONField(ordinal = 40)
    private String pwd;

    @JsonProperty(index = 40)
    @JSONField(ordinal = 45)
    private int timeout = 8;

    @JsonProperty(index = 50)
    @JSONField(ordinal = 50)
    private String desc;

    @JsonProperty(index = 60)
    @JSONField(ordinal = 60)
    private boolean dockerEnabled = true;

    @JsonProperty(index = 70)
    @JSONField(ordinal = 70)
    private String kubeConfig;

    @JsonProperty(index = 70)
    @JSONField(ordinal = 70)
    private String registryUrl;

    @JsonProperty(index = 75)
    @JSONField(ordinal = 75)
    private String registryContextPath;

    @JsonProperty(index = 80)
    @JSONField(ordinal = 80)
    private String registryUsername;

    @JsonProperty(index = 90)
    @JSONField(ordinal = 90)
    private String registryPassword;

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof ServerLoginParam) {
            ServerLoginParam s = (ServerLoginParam) obj;
            return this.ip.trim().equals(s.ip.trim()) && (this.port == s.port) && this.user.trim().equals(s.user.trim()) && this.pwd.trim().equals(s.pwd.trim());
        }
        return super.equals(obj);
    }

    @Override
    public String toString() {
        return super.toString();
    }
}

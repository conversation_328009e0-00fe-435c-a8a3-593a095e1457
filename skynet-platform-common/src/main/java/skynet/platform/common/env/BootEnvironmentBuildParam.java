package skynet.platform.common.env;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import skynet.boot.common.domain.Jsonable;

import java.util.List;

/**
 * 构建服务环境
 */
@Setter
@Getter
@Accessors(chain = true)
public class BootEnvironmentBuildParam extends Jsonable {

    /**
     * 服务坐标
     */
    private String actionPoint;

    /**
     * 服务标识
     */
    private String actionId;

    /**
     * masterIp （一般是K8s集群的masterIp）
     */
    private String masterIp;
    /**
     * 服务 IP （宿主主机的IP）
     */
    private String ipAddress;

    /**
     * 服务主端口
     */
    private Integer appPort;

    /**
     * 服务扩展端口
     */
    private List<Integer> extPorts;

    /***
     * 服务序号，默认从0开始 （场景一个节点多实例时）
     */
    private int actionIndex;
}

package skynet.platform.manager.backup.config;

import lombok.Getter;
import lombok.Setter;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.StringUtils;
import skynet.boot.SkynetProperties;

@Getter
@Setter
@Configuration(proxyBeanMethods = false)
@ConfigurationProperties("skynet.backup")
public class BackupProperties {

    private final SkynetProperties skynetProperties;

    public BackupProperties(SkynetProperties skynetProperties) {
        this.skynetProperties = skynetProperties;
    }

    /**
     * 是否开启备份
     */
    private boolean enabled = true;

    /**
     * 备份目录，默认：{skynet_home}/backup
     */
    private String backupDir;

    /**
     * 自动备份时间间隔，默认 30 分钟
     */
    private Integer interval = 30;

    public String getBackupDir() {
        return StringUtils.hasText(backupDir) ? backupDir : String.format("%s/backup", skynetProperties.getHome());
    }
}

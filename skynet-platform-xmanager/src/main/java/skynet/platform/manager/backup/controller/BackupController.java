package skynet.platform.manager.backup.controller;

import java.util.List;

import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.BackupDto;
import skynet.platform.feign.model.BackupRestoreDto;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3Backup;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.backup.service.BackupService;

@Slf4j
@RestController
@ExposeSwagger2
public class BackupController implements V3Backup {
    
    private final BackupService backupService;

    public BackupController(BackupService backupService) {
        this.backupService = backupService;
    }

    @Override
    @AuditLog(module = "备份管理", operation = "创建备份", message = "")
    public SkynetApiResponse<BackupDto> createBackup() throws Exception {
        SkynetApiResponse<BackupDto> response = new SkynetApiResponse<>();
        BackupDto result = backupService.createBackup();
        response.setData(result);
        log.debug("createBackup response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<List<BackupDto>> getBackups() throws Exception {
        SkynetApiResponse<List<BackupDto>> response = new SkynetApiResponse<>();
        List<BackupDto> results = backupService.getBackups();
        response.setData(results);
        log.debug("getBackups response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<BackupDto> getBackup(String backupName) throws Exception {
        SkynetApiResponse<BackupDto> response = new SkynetApiResponse<>();
        BackupDto result = backupService.getBackup(backupName);
        response.setData(result);
        log.debug("getBackup response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "备份管理", operation = "删除备份", message = "backupName=#{#backupName}")
    public SkynetApiResponse<BackupDto> deleteBackup(String backupName) throws Exception {
        SkynetApiResponse<BackupDto> response = new SkynetApiResponse<>();
        BackupDto result = backupService.deleteBackup(backupName);
        response.setData(result);
        log.debug("deleteBackup response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "备份管理", operation = "恢复备份", message = "backupRestoreDto=#{#backupRestoreDto}")
    public SkynetApiResponse<BackupDto> restoreBackup(BackupRestoreDto backupRestoreDto) throws Exception {
        SkynetApiResponse<BackupDto> response = new SkynetApiResponse<>();
        BackupDto result = backupService.restoreBackup(backupRestoreDto);
        response.setData(result);
        log.debug("restoreBackup response:{}", response);
        return response;
    }
}

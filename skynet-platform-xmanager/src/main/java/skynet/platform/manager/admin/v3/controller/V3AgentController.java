package skynet.platform.manager.admin.v3.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;
import skynet.platform.feign.model.AgentConfigBlocksDto;
import skynet.platform.feign.model.AgentDto;
import skynet.platform.feign.model.NoDataResponse;
import skynet.platform.feign.service.V3Agent;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.exception.AgentConnectionException;

@Profile("!mock")
@Slf4j
@RestController
@ExposeSwagger2
@ConditionalOnApiV3Enabled
public class V3AgentController implements V3Agent {

    private final V3AgentService v3AgentService;

    public V3AgentController(V3AgentService v3AgentService) {
        this.v3AgentService = v3AgentService;
    }

    @Override
    public GetAgentsResponse getAgents() {
        log.debug("getAgents");
        GetAgentsResponse response = new GetAgentsResponse();
        try {
            response.setData(v3AgentService.getAgents());

        } catch (ApiRequestException e) {
            log.error("getAgents error= {}", e.getMessage());
            response.setException(e);
        } catch (Exception e) {
            log.error("getAgents error.", e);
            response.setException(e);
        }
        log.debug("getAgents response={}", response);
        return response;
    }

    @Override
    public GetAgentResponse getAgent(@PathVariable String ip) {
        log.debug("getAgent. ip={}", ip);
        GetAgentResponse response = new GetAgentResponse();
        try {
            response.setData(v3AgentService.getAgent(ip));

        } catch (ApiRequestException e) {
            log.error("getAgents error= {}", e.getMessage());
            response.setException(e);
        } catch (Exception e) {
            log.error("getAgent error. ip={}", ip, e);
            response.setException(e);
        }
        log.debug("getAgents response={}", response);
        return response;
    }

    @Override
    @AuditLog(module = "Agent管理", operation = "创建Agent节点", message = "create Agent.IP=#{#agentDto.ip},Detail=#{#agentDto}")
    public NoDataResponse createAgent(@RequestBody AgentDto agentDto) {
        log.debug("createAgent. agentDto={}", agentDto);
        NoDataResponse response = new NoDataResponse();
        try {
            v3AgentService.create(agentDto);
        } catch (AgentConnectionException agentConnectEx) {
            response.setCode(ApiRequestErrorCode.AGENT_SSH_ERROR.getCode());
            response.setMessage(ApiRequestErrorCode.AGENT_SSH_ERROR.getValue());
        } catch (Exception e) {
            log.error("createAgent error. agentDto={}", agentDto, e);
            response.setException(e);
        }
        log.debug("createAgent response={}", response);
        return response;
    }

    @Override
    @AuditLog(module = "Agent管理", operation = "更新参数", message = "更新Agent ip = #{#ip},param=#{param}")
    public NoDataResponse update(@PathVariable String ip, @RequestBody AgentDto param) {
        log.info("update. ip={}, param={}", ip, param);
        NoDataResponse response = new NoDataResponse();
        try {
            v3AgentService.update(ip, param);
        } catch (AgentConnectionException agentConnectEx) {
            response.setCode(ApiRequestErrorCode.AGENT_SSH_ERROR.getCode());
            response.setMessage(ApiRequestErrorCode.AGENT_SSH_ERROR.getValue());
        } catch (Exception e) {
            log.error("update error. ip={}, param={}", ip, param, e);
            response.setException(e);
        }
        log.debug("update response={}", response);
        return response;
    }

    @Override
    @AuditLog(module = "Agent管理配置块管理", operation = "更新参数", message = "更新Agent ip = #{#ip},param=#{param}")
    public NoDataResponse updateConfigBlocks(String ip, AgentConfigBlocksDto param) {
        log.info("update. ip={}, param={}", ip, param);
        NoDataResponse response = new NoDataResponse();
        try {
            v3AgentService.update(ip, param);
        } catch (Exception e) {
            log.error("update error. ip={}, param={}", ip, param, e);
            response.setException(e);
        }
        log.debug("update response={}", response);
        return response;
    }

    @Override
    @AuditLog(module = "Agent管理", operation = "删除Agent节点", message = "IP=#{#ip};StopAction=#{#stopAction}")
    public NoDataResponse delete(@PathVariable String ip,
                                 @RequestParam(name = "stopAction", required = false, defaultValue = "false") boolean stopAction) {
        log.info("delete. ip={}", ip);
        NoDataResponse response = new NoDataResponse();
        try {
            v3AgentService.delete(ip, stopAction);
        } catch (Exception e) {
            log.error("delete error. ip={}", ip, e);
            response.setException(e);
        }
        log.debug("delete response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "Agent管理", operation = "启停节点", message = "IP=#{#ip};operation=#{#operation};stopActions=#{#stopActions}")
    public NoDataResponse status(@PathVariable String ip,
                                 @RequestParam(name = "operation") String operation,
                                 @RequestParam(name = "stopActions", required = false, defaultValue = "false") boolean stopActions) {
        log.debug("status. ip:{} operation:{}", ip, operation);
        NoDataResponse response = new NoDataResponse();
        try {
            v3AgentService.status(ip, operation, stopActions);
        } catch (Exception e) {
            log.error("status error. ip:{}, action:{}", ip, operation, e);
            response.setException(e);
        }
        log.debug("status response:{}", response);
        return response;
    }
}

//package skynet.platform.manager.admin.v3.controller.mock;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Profile;
//import skynet.platform.feign.model.ActionDefinitionDto;
//import skynet.platform.feign.model.NoDataResponse;
//import skynet.platform.manager.admin.v3.controller.V3ActionDefinitionController;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.when;
//
//
//@Profile("mock")
//@Configuration
//public class V3ActionDefinitionControllerConfig {
//
//    @Bean("MockV3ActionDefinitionController")
//    public V3ActionDefinitionController newMock() {
//        V3ActionDefinitionController ret = mock(V3ActionDefinitionController.class);
//        V3ActionDefinitionController.GetDefinitionResponse resp = new V3ActionDefinitionController.GetDefinitionResponse();
//        resp.setCode(111);
//        resp.setMessage("mock");
//        try {
//            when(ret.getDefinitions(any(), any())).thenReturn(getDefinitions());
//            when(ret.getDefinition(any())).thenReturn(getDefinition());
//            when(ret.createDefinition(any())).thenReturn(createDefinition());
//            when(ret.deleteDefinition(any())).thenReturn(deleteDefinition());
//            when(ret.updateDefinition(any(), any())).thenReturn(updateDefinition());
//
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return ret;
//    }
//
//    private V3ActionDefinitionController.GetDefinitionsResponse getDefinitions() {
//        V3ActionDefinitionController.GetDefinitionsResponse response = new V3ActionDefinitionController.GetDefinitionsResponse();
//        response.setCode(0);
//        response.setMessage("mock");
//        List<ActionDefinitionDto> data = new ArrayList<>();
//        data.add(mockActionDefinitionDto());
//        response.setData(data);
//
//        return response;
//    }
//
//    private V3ActionDefinitionController.GetDefinitionResponse getDefinition() {
//        V3ActionDefinitionController.GetDefinitionResponse response = new V3ActionDefinitionController.GetDefinitionResponse();
//        response.setCode(0);
//        response.setMessage("mock");
//        response.setData(mockActionDefinitionDto());
//        return response;
//    }
//
//    private NoDataResponse createDefinition() {
//        return mockNoDataResponse();
//    }
//
//    private NoDataResponse deleteDefinition() {
//        return mockNoDataResponse();
//    }
//
//    private NoDataResponse updateDefinition() {
//        return mockNoDataResponse();
//    }
//
//    private NoDataResponse mockNoDataResponse() {
//        NoDataResponse noDataResponse = new NoDataResponse();
//        noDataResponse.setCode(0);
//        noDataResponse.setMessage("mock");
//        return noDataResponse;
//    }
//
//    private ActionDefinitionDto mockActionDefinitionDto() {
//        ActionDefinitionDto actionDefinitionDto = new ActionDefinitionDto();
//        actionDefinitionDto.setActionCode("rest-test-v1");
//        actionDefinitionDto.setPluginCode("turing-test");
//        actionDefinitionDto.setActionName("rest-test-v1@turing-test");
//        actionDefinitionDto.setDescription("test service");
//        actionDefinitionDto.setType("springBoot");
//        actionDefinitionDto.setProtocol("http");
//        actionDefinitionDto.setHomePageURL("/index");
//        List<ActionDefinitionDto.ReferencedFileDto> referencedFiles = new ArrayList<>();
//        ActionDefinitionDto.ReferencedFileDto referencedFileDto = new ActionDefinitionDto.ReferencedFileDto();
//        referencedFileDto.setFileName("test-service.zip");
//        referencedFileDto.setTargetDir("/iflytek/server/skynet/plugin");
//        referencedFiles.add(referencedFileDto);
//        actionDefinitionDto.setReferencedFiles(referencedFiles);
//        ActionDefinitionDto.StartupConfigDto startupConfigDto = new ActionDefinitionDto.StartupConfigDto();
//        startupConfigDto.setWorkingDir("/iflytek/server/skynet/plugin/turing-test/lib");
//        startupConfigDto.setRunnableJar("test-service.jar");
//        startupConfigDto.setJavaCmdOptions("-Xms=512m -Xmx=1g");
//        startupConfigDto.setProgramArguments("--my-config=xxx");
//        Map<String, String> sysEnvironments = new HashMap<>();
//        sysEnvironments.put("ENGINE_TEST_HOME", "/iflytek/engine/lib/test");
//        startupConfigDto.setSysEnvironments(sysEnvironments);
//        startupConfigDto.setSignalToStop(9);
//        actionDefinitionDto.setStartupConfig(startupConfigDto);
//        ActionDefinitionDto.HealthCheckConfig healthCheckConfig = new ActionDefinitionDto.HealthCheckConfig();
//        healthCheckConfig.setType("");
//        healthCheckConfig.setUrl("/health");
//        healthCheckConfig.setDelaySeconds(30);
//        healthCheckConfig.setIntervalSeconds(20);
//        healthCheckConfig.setTimeoutSeconds(20);
//        healthCheckConfig.setRetryTimes(3);
//        actionDefinitionDto.setHealthCheckConfig(healthCheckConfig);
//        ActionDefinitionDto.IntegrationConfigDto integrationConfigDto = new ActionDefinitionDto.IntegrationConfigDto();
////        integrationConfigDto.setMeshEnabled(false);
//        integrationConfigDto.setLogbackLogCollection(true);
//        actionDefinitionDto.setIntegrationConfig(integrationConfigDto);
//        actionDefinitionDto.setProperties("turing.properties.one=one\n" +
//                "turing.properties.two=two");
//        actionDefinitionDto.setLoggingLevels("skynet.boot=INFO\n" +
//                "com.iflytek=INFO");
//        return actionDefinitionDto;
//    }
//}

//package skynet.platform.manager.admin.v3.controller.mock;
//
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Profile;
//import skynet.platform.feign.model.NoDataResponse;
//import skynet.platform.feign.model.PluginDto;
//import skynet.platform.feign.model.SkynetApiResponse;
//import skynet.platform.manager.admin.v3.controller.V3PluginController;
//
//import java.util.ArrayList;
//import java.util.List;
//
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.when;
//
//@Profile("mock")
//@Configuration
//public class V3PluginControllerConfig {
//    @Bean("MockV3PluginController")
//    public V3PluginController newMock() {
//        V3PluginController i = mock(V3PluginController.class);
//
//        try {
//            when(i.createPlugin(new PluginDto())).thenReturn(SkynetApiResponse.success());
//            when(i.deletePlugin("")).thenReturn(new NoDataResponse());
//            when(i.getAllPlugins()).thenReturn(getAllPluinsResp());
//            when(i.getPlugin("123@345")).thenReturn(SkynetApiResponse.success(getPluginDto()));
//            when(i.updatePlugin("123@456", new PluginDto())).thenReturn(new NoDataResponse());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return i;
//    }
//
//    private SkynetApiResponse<List<PluginDto>> getAllPluinsResp() {
//        PluginDto pd = new PluginDto();
//        pd.setVersion("2000");
//        pd.setName("tuling-abc");
//        pd.setDescription("this is abc");
//        pd.setCode("code-here");
//
//        List<PluginDto> pluginDtoList = new ArrayList<>();
//        pluginDtoList.add(pd);
//        SkynetApiResponse<List<PluginDto>> s = new SkynetApiResponse<>();
//        s.setData(pluginDtoList);
//        return s;
//    }
//
//    private PluginDto getPluginDto() {
//        PluginDto pd = new PluginDto();
//        pd.setVersion("2000");
//        pd.setName("tuling-abc");
//        pd.setDescription("this is abc");
//        pd.setCode("code-here");
//        return pd;
//    }
//}

package skynet.platform.manager.admin.v3;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import skynet.platform.common.domain.BootAction;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.model.ActionDeploymentDto;
import skynet.platform.feign.model.AgentDeploymentDto;
import skynet.platform.feign.model.BootType;
import skynet.platform.feign.model.PluginDto;
import skynet.platform.manager.admin.domain.BootServerView;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * v2 接口与 v3 数据类型转换类
 *
 * <AUTHOR>
 */
public class DataTypeConverter {
    private static final ObjectMapper MAPPER = new ObjectMapper();

    public static PluginDto toPluginDto(NodeDescription nd) {
        PluginDto pd = new PluginDto();
        pd.setCode(nd.getCode());
        pd.setDescription(nd.getDesc());
        pd.setName(nd.getName());
        pd.setVersion(nd.getVersion());
        pd.setIndex(nd.getIndex());
        return pd;
    }

    public static List<PluginDto> batchToPluginDto(List<NodeDescription> nds) {
        List<PluginDto> pluginDtoList = new ArrayList<>();
        for (NodeDescription nd : nds) {
            pluginDtoList.add(toPluginDto(nd));
        }
        return pluginDtoList;
    }

    public static NodeDescription toNodeDescription(PluginDto pd) {
        NodeDescription nd = new NodeDescription();
        nd.setCode(pd.getCode());
        nd.setDesc(pd.getDescription());
        nd.setName(pd.getName());
        nd.setVersion(pd.getVersion());
        nd.setIndex(pd.getIndex());
        return nd;
    }

    /**
     * BootServerView 属性转 AgentDeploymentDto
     *
     * @param bsv BootServerView
     * @return
     */
    public static AgentDeploymentDto toAgentDeploymentDto(BootServerView bsv, String agentStatus,
                                                          List<ActionDeploymentDto> actionDeploymentDtoList) {
        AgentDeploymentDto agent = new AgentDeploymentDto();
        agent.setIndex(bsv.getIndex());
        agent.setIp(bsv.getIp());
        agent.setAgentPort(bsv.getPort());
        agent.setAgentActionPoint(bsv.getAid());
        agent.setAgentActionID(bsv.getAid());
        agent.setAgentType(bsv.getAgentType());
        agent.setServerTags(bsv.getTag());
        Map<String, Object> m = MAPPER.convertValue(bsv.getSys(), new TypeReference<Map<String, Object>>() {
        });
        agent.setServerInfo(m);
        agent.setAgentStatus(agentStatus);
        agent.setActions(actionDeploymentDtoList);
        return agent;
    }

    /**
     * BootAction 属性转 ActionDeploymentDto
     *
     * @param ba BootAction
     * @return ActionDeploymentDto
     */
    public static ActionDeploymentDto toActionDeploymentDto(BootAction ba, String pluginName, String protocol, String homePageUrl,
                                                            String startTime, String upTime, String status) {
        ActionDeploymentDto a = new ActionDeploymentDto();
        a.setActionPoint(getActionPoint(ba));
        a.setActionID(ba.getAid());
        a.setActionName(ba.getName());
        a.setBootType(BootType.parse(ba.getBootType().toString()));
        a.setPluginCode(ba.getPlugin());
        a.setPort(ba.getPort());
        a.setPid(ba.getPid());
        a.setEnabled(ba.isEnable());
        a.setReplicas(ba.getReplicas());
        a.setNodeSelector(ba.getNodeSelector());
        a.setProtocol(protocol);
        a.setHomePageURL(homePageUrl);
        a.setPluginName(pluginName);
        a.setStartupOrder(ba.getIndex());
        a.setStartTime(startTime);
        a.setUpTime(upTime);
        a.setStatus(status);
        a.setWorkloadType(ba.getWorkloadType());
        return a;
    }

    public static String getActionPoint(BootAction ba) {
        return String.format("%s@%s", ba.getCode(), ba.getPlugin());
    }
}

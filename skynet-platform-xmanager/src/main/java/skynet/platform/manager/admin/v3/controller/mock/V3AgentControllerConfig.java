//package skynet.platform.manager.admin.v3.controller.mock;
//
//import com.alibaba.fastjson2.JSON;
//import com.alibaba.fastjson2.TypeReference;
//import org.apache.commons.io.IOUtils;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Profile;
//import org.springframework.core.io.ClassPathResource;
//import skynet.platform.feign.model.AgentDto;
//import skynet.platform.feign.model.NoDataResponse;
//import skynet.platform.manager.admin.v3.controller.V3AgentController;
//
//import java.io.IOException;
//import java.nio.charset.StandardCharsets;
//import java.util.List;
//
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.when;
//
//@Profile("mock")
//@Configuration
//public class V3AgentControllerConfig {
//
//    @Bean("MockV3AgentController")
//    public V3AgentController newMock() {
//        V3AgentController mock = mock(V3AgentController.class);
//        try {
//            when(mock.getAgents()).thenReturn(getAgents());
//            when(mock.getAgent(any())).thenReturn(getAgent());
//            when(mock.createAgent(any())).thenReturn(createAgent());
//            when(mock.update(any(), any())).thenReturn(update());
//            when(mock.delete(any(), false)).thenReturn(delete());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return mock;
//    }
//
//    private V3AgentController.GetAgentsResponse getAgents() throws IOException {
//        V3AgentController.GetAgentsResponse ret = new V3AgentController.GetAgentsResponse();
//        String s = IOUtils.toString(new ClassPathResource("mock/agents.js").getInputStream(), StandardCharsets.UTF_8);
//        List<AgentDto> data = JSON.parseObject(s, new TypeReference<List<AgentDto>>() {
//        });
//        ret.setData(data);
//        return ret;
//    }
//
//    private V3AgentController.GetAgentResponse getAgent() throws IOException {
//        V3AgentController.GetAgentResponse ret = new V3AgentController.GetAgentResponse();
//        String s = IOUtils.toString(new ClassPathResource("mock/agents.js").getInputStream(), StandardCharsets.UTF_8);
//        List<AgentDto> data = JSON.parseObject(s, new TypeReference<List<AgentDto>>() {
//        });
//        ret.setData(data.get(0));
//        return ret;
//    }
//
//    private NoDataResponse createAgent() {
//        return mockNoDataResponse();
//    }
//
//    private NoDataResponse update() {
//        return mockNoDataResponse();
//    }
//
//    private NoDataResponse delete() {
//        return mockNoDataResponse();
//    }
//
//    private NoDataResponse mockNoDataResponse() {
//        NoDataResponse noDataResponse = new NoDataResponse();
//        noDataResponse.setCode(0);
//        noDataResponse.setMessage("mock");
//        return noDataResponse;
//    }
//}

package skynet.platform.manager.admin.v3.service.impl;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.boot.SkynetProperties;
import skynet.platform.common.repository.config.setting.SkynetSettingManager;
import skynet.platform.feign.model.ClusterInfoDto;
import skynet.platform.manager.admin.v3.service.V3ClusterService;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;

import java.io.IOException;

/**
 * <AUTHOR> by jianwu6 on 2020/7/28 17:14
 */
@Slf4j
@Service
@ConditionalOnApiV3Enabled
public class V3ClusterServiceImpl extends V3BaseServiceImpl implements V3ClusterService {

    private final SkynetSettingManager skynetSettingManager;
    private final SkynetProperties skynetProperties;

    public V3ClusterServiceImpl(SkynetSettingManager skynetSettingManager, SkynetProperties skynetProperties) {
        this.skynetSettingManager = skynetSettingManager;
        this.skynetProperties = skynetProperties;
    }

    @Override
    public ClusterInfoDto getInfo() {
        ClusterInfoDto clusterInfoDto = new ClusterInfoDto();
        clusterInfoDto.setClusterName(this.antConfigService.getClusterName());
        clusterInfoDto.setZkServers(this.antConfigService.getZookeeperServers());
        clusterInfoDto.setSkynetHome(skynetProperties.getHome());
        return clusterInfoDto;
    }

    @Override
    public String getProperties() throws IOException {
        return skynetSettingManager.getPropertiesService().getGlobalProps();
    }

    @Override
    public String getLoggingLevels() throws IOException {
        return skynetSettingManager.getLoggerServices().getGlobalProps();
    }

    @Override
    public void updateProperties(String properties) {
//        if (StringUtils.isBlank(properties)) {
//            throw new ApiRequestException(ApiRequestErrorCode.PARAM_ILLEGAL);
//        }
        skynetSettingManager.getPropertiesService().setProps(StringUtils.EMPTY, StringUtils.EMPTY, properties);
    }

    @Override
    public void updateLoggingLevels(String loggingLevels) {
//        if (StringUtils.isBlank(loggingLevels)) {
//            throw new ApiRequestException(ApiRequestErrorCode.PARAM_ILLEGAL);
//        }
        skynetSettingManager.getLoggerServices().setProps(StringUtils.EMPTY, StringUtils.EMPTY, loggingLevels);
    }
}

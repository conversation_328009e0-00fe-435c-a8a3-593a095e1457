package skynet.platform.manager.admin.v3.service;

import skynet.platform.feign.model.AgentConfigBlocksDto;
import skynet.platform.feign.model.AgentDto;

import java.util.List;

/**
 * Agent(服务器)管理
 *
 * <AUTHOR> by jianwu6 on 2020/7/28 15:00
 */
public interface V3AgentService extends V3BaseService {

    /**
     * 获取所有的agent
     *
     * @return
     */
    List<AgentDto> getAgents() throws Exception;

    /**
     * 获取指定ip的agent
     *
     * @param ip
     * @return
     */
    AgentDto getAgent(String ip);

    /**
     * 添加agent
     *
     * @param dto
     */
    void create(AgentDto dto) throws Exception;

    /**
     * 更新agent
     *
     * @param ip
     * @param dto
     */
    void update(String ip, AgentDto dto) throws Exception;

    /**
     * 更新agent 配置块信息
     *
     * @param ip
     * @param dto
     */
    void update(String ip, AgentConfigBlocksDto dto) throws Exception;

    /**
     * 删除agent
     *
     * @param ip
     * @param stopAction 是否同时停止托管的Action
     */
    void delete(String ip, boolean stopAction) throws Exception;

    /**
     * 启动 停止 agent
     *
     * @param ip
     * @param operate start：启动 stop：停止
     */
    void status(String ip, String operate, boolean stopAction) throws Exception;

    /**
     * 测试节点是否可以正常连接
     * 
     * @param dto
     * @throws Exception
     */
    void testAgentConnection(AgentDto dto) throws Exception;

    /**
     * 部署 agent
     * 
     * @param ip
     * @param dockerEnabled
     * @param isForce
     * @return
     * @throws Exception
     */
    String deployAgent(String ip, boolean dockerEnabled, boolean isForce) throws Exception;
}

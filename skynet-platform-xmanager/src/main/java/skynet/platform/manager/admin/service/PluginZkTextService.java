package skynet.platform.manager.admin.service;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.boot.common.utils.DateUtil;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.config.setting.SkynetConfigType;
import skynet.platform.common.repository.domain.ActionNameContract;
import skynet.platform.common.repository.domain.CommentProperties;
import skynet.platform.common.repository.domain.NodeDescription;
import skynet.platform.feign.exception.PluginNotExistException;

import java.io.IOException;
import java.util.*;


/**
 * 插件ZK text 配置 导入 导出服务
 * <p>
 * 导出应用配置时，增加集群级别属性日志，
 * <p>
 * 导入时，集群级别属性以前的属性合并
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class PluginZkTextService {

    private static final String CLUSTER_NAME_SYMBOL = "/{CLUSTER_NAME}/";
    private final IAntConfigService antConfigService;

    public PluginZkTextService(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
    }

    public String exportZkConfig(String pluginCode) throws PluginNotExistException {
        return this.exportZkConfig(Arrays.asList(pluginCode));
    }

    /**
     * 导出 ActionPoint 或子系统（插件）定义
     *
     * @param codes
     * @return
     * @throws PluginNotExistException
     */
    public String exportZkConfig(List<String> codes) throws PluginNotExistException {

        Map<String, String> allMap = new TreeMap<>();
        boolean isPlugin = true;
        for (String code : codes) {
            String path = "";
            //判断是 plugin  还是 actionPoint
            if (code.indexOf("@") > 0) {
                ActionNameContract actionNameContract = new ActionNameContract(code);
                path = this.antConfigService.getActionPath(actionNameContract.getPluginCode(), actionNameContract.getActionCode());
                isPlugin = false;
            } else {
                NodeDescription nodeDescription = this.antConfigService.getPlugin(code);
                if (nodeDescription == null) {
                    throw new PluginNotExistException(code);
                }
                log.debug("exportZkConfig plugin={}", code);
                path = String.format("%s/%s", this.antConfigService.getSkynetPluginPath(), code);
                isPlugin = true;
            }

            Map<String, String> pluginData = this.antConfigService.getZkConfigService().exportData(path);
            pluginData.forEach(allMap::put);
        }

        //如果有插件，就包含全局的属性配置   不包含全局属性配置，因为会带来副作用。 by lyhu   2021年09月27日12:07:01
//        if (isPlugin) {
//            String settingPath = this.antConfigService.getSettingPath(null, null);
//            Map<String, String> globalMap = this.antConfigService.getZkConfigService().exportData(settingPath);
//            globalMap.forEach((k, v) -> allMap.put(k, v));
//        }

        StringBuilder stringBuilder = new StringBuilder();
        String exportDateTime = DateUtil.getCurrentDateInfo();
        String zkServers = this.antConfigService.getZookeeperServers();
        String exportDesc = String.format("# Dumped at %s: zookeeper[%s]\r\n", exportDateTime, zkServers);
        stringBuilder.append(exportDesc);
        allMap.forEach((k, v) -> stringBuilder.append(k.replaceFirst(String.format("/%s/", this.antConfigService.getClusterName()), CLUSTER_NAME_SYMBOL))
                .append("=").append(v).append("\r\n"));
        log.trace("codes={};zkConfig={}\r\n", codes, stringBuilder);
        return stringBuilder.toString();
    }


    /**
     * 导入服务定义文件
     *
     * @param zkConfigLines
     * @throws IOException
     * @throws InterruptedException
     */
    public void importZkConfig(String zkConfigLines) throws IOException, InterruptedException {
        List<String> objList = Arrays.asList(zkConfigLines.split(System.lineSeparator()));
        this.importZkConfig(objList);
    }

    public void importZkConfig(List<String> zkConfigLines) throws IOException, InterruptedException {

        String replacement = String.format("/%s/", this.antConfigService.getClusterName());
        String settingPath = this.antConfigService.getSettingPath();
        String loggerPath = String.format("%s=%s=", settingPath, SkynetConfigType.LOGGER.getContextPath());
        String propertiesPath = String.format("%s=%s=", settingPath, SkynetConfigType.PROPERTY.getContextPath());

        List<String> lineList = new ArrayList<>();
        for (String config : zkConfigLines) {
            String line = config.replace(CLUSTER_NAME_SYMBOL, replacement);
            if (line.startsWith(propertiesPath)) {
                line = combineProps(settingPath, SkynetConfigType.PROPERTY, line, line.replaceFirst(propertiesPath, ""));
            } else if (line.startsWith(loggerPath)) {
                line = combineProps(settingPath, SkynetConfigType.LOGGER, line, line.replaceFirst(loggerPath, ""));
            }
            if (StringUtils.isNoneBlank(line)) {
                lineList.add(line);
            }
        }
        this.antConfigService.getZkConfigService().importData(lineList, true);
    }

    private String combineProps(String settingPath, SkynetConfigType skynetConfigType, String thisLine, String thisProps) throws IOException {

        //本次导入 全局属性 为空，不导入
        if (StringUtils.isBlank(thisProps)) {
            return null;
        }
        //获取已经存在的全局属性，如果为空，直接使用本次的属性
        String path = String.format("%s/%s", settingPath, skynetConfigType.getContextPath());
        String lastProps = this.antConfigService.getData(path);
        if (StringUtils.isBlank(lastProps)) {
            return thisLine;
        }

        CommentProperties lastProperties = new CommentProperties();
        lastProperties.load(lastProps);
        CommentProperties thisProperties = new CommentProperties();
        thisProps = thisProps.replaceAll("\\\\n", System.lineSeparator());
        thisProperties.load(thisProps);

        //追加替换
        thisProperties.getAllProperty().forEach((k, v) -> lastProperties.setProperty(k, v, thisProperties.getComment(k)));

        return String.format("%s=%s=%s", settingPath, skynetConfigType.getContextPath(), lastProperties.store());
    }
}

package skynet.platform.manager.admin.v3.service.impl;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.feign.model.SwitchLabelTypeDto;
import skynet.platform.manager.admin.v3.service.V3SwitchLabelTypeService;
import skynet.platform.manager.annotation.ConditionalOnApiV3Enabled;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@ConditionalOnApiV3Enabled
public class V3SwitchLabelTypeServiceImpl implements V3SwitchLabelTypeService {

    private static final String LABEL_PATH = "/xmanager/action/label";
    private final IAntConfigService antConfigService;

    public V3SwitchLabelTypeServiceImpl(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
    }

    @Override
    public List<SwitchLabelTypeDto> getAll() {
        List<SwitchLabelTypeDto> ret = new ArrayList<>();
        String strLabels = antConfigService.getData(String.format("/%s%s", antConfigService.getClusterName(), LABEL_PATH));
        log.trace("get all SwitchLabelTypeDto: ", strLabels);
        if (StringUtils.isNotEmpty(strLabels)) {
            ret = JSON.parseArray(strLabels, SwitchLabelTypeDto.class);
        }
        return ret;
    }
}

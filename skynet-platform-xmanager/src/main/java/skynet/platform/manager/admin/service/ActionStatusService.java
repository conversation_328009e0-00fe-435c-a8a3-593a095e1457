package skynet.platform.manager.admin.service;

import com.alibaba.fastjson2.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.client.RestTemplate;
import skynet.boot.common.concurrent.ParallelService;
import skynet.boot.security.auth.AuthUtils;
import skynet.platform.common.AppBootEnvironment;
import skynet.platform.common.domain.AntServerControlType;
import skynet.platform.common.domain.BootStatus;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.AntActionStatus;
import skynet.platform.common.repository.domain.AntNodeState;
import skynet.platform.manager.admin.config.ManagerProperties;
import skynet.platform.manager.admin.domain.BootServerView;
import skynet.platform.manager.admin.domain.BootWorkerView;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static java.util.stream.Collectors.toList;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ActionStatusService extends BaseService {

    private final IAntConfigService antConfigService;
    private final RestTemplate authRestTemplate;
    private final ManagerProperties managerProperties;

    public ActionStatusService(IAntConfigService antConfigService,
                               @Qualifier("authRestTemplate") RestTemplate authRestTemplate,
                               ManagerProperties managerProperties) {
        super(managerProperties, antConfigService);
        this.antConfigService = antConfigService;
        this.authRestTemplate = authRestTemplate;
        this.managerProperties = managerProperties;
    }

    private static BootServerView getBootServerView(RestTemplate signAuthRestTemplate, AntActionStatus input) throws Exception {
        long start = System.currentTimeMillis();

        String httpUri = String.format("http://%s:%d/skynet/agent/ctrl/%s", input.getIp(), input.getPort(),
                AntServerControlType.FETCH_SERVER_STATE).replaceAll("\"", "");
        try {
            AntNodeState antNodeState = signAuthRestTemplate.getForObject(httpUri, AntNodeState.class);

            log.debug("fetch the agent[{}] cost={}ms;", input.getIp(), (System.currentTimeMillis() - start));
            log.debug("fetch the agent[{}] state:\r\n{}", input.getIp(), antNodeState);

            if (antNodeState == null) {
                log.warn("fetch the agent[{}] state is null.", input.getIp());
                return null;
            }

            BootServerView bootServerView = new BootServerView(antNodeState);
            httpUri = String.format("http://%s:%d/skynet/agent/ctrl/%s", input.getIp(), input.getPort(),
                    AntServerControlType.FETCH_WORKERS_STATE).replaceAll("\"", "");

            JSONArray jsonArray = signAuthRestTemplate.getForObject(httpUri, JSONArray.class);

            log.debug("Fetch all workers[{}] cost={}ms;", input.getIp(), (System.currentTimeMillis() - start));
            log.trace("Fetch all workers[{}] state:\r\n{}", input.getIp(), jsonArray);

            if (jsonArray == null) {
                log.warn("Fetch the agent[{}] workers state is null.", input.getIp());
                return bootServerView;
            }

            List<BootWorkerView> stateT = jsonArray.toJavaList(BootWorkerView.class);

            if (stateT != null) {
                for (BootWorkerView worker : stateT) {
                    bootServerView.getWorkers().add(worker);
                }
            }
            log.debug("Fetch the agent [{}] status cost={}ms; ", input.getIp(), (System.currentTimeMillis() - start));
            return bootServerView;
        } catch (org.springframework.web.client.HttpClientErrorException e) {
            String authErr = Objects.requireNonNull(e.getResponseHeaders()).getFirst(AuthUtils.SKYNET_AUTH_ERR_MESSAGE);
            if (StringUtils.isNoneBlank((authErr))) {
                log.error("Fetch the server [ip={}] status vim [{}] error={}", input.getIp(), httpUri, authErr);
            } else {
                log.error(String.format("Fetch the server [ip:%s] status vim [%s] error:%s", input.getIp(), httpUri, e.getMessage()));
            }
        } catch (Exception e) {
            log.error(String.format("Fetch the server [ip:%s] status vim [%s] error:%s", input.getIp(), httpUri, e.getMessage()));
        }
        return null;
    }

    /**
     * 获取所有在线 agent 信息 (仅在线状态)
     *
     * @return
     * @throws Exception
     */
    public List<BootServerView> getServerStatus() throws Exception {
        return getServerStatusList(null);
    }

    /**
     * 获取指定在线IP agent 信息 (仅在线状态)
     *
     * @return
     * @throws Exception
     */
    public BootServerView getServerStatus(String ip) throws Exception {
        Assert.hasText(ip, "The ip is blank.");
        List<BootServerView> objList = getServerStatusList(ip);
        return !objList.isEmpty() ? objList.get(0) : null;
    }

    private List<BootServerView> getServerStatusList(String ip) throws Exception {
        long cost = System.currentTimeMillis();

        log.debug("getServerStatus...");

        // 从在线服务里面获取列表
        Map<String, AntActionStatus> onlineAgentState = this.antConfigService.getOnlineActionNodeStatus(AppBootEnvironment.AGENT_ACTION_POINT, null);
        List<BootServerView> onlineServers = new ArrayList<>(onlineAgentState.size());
        log.debug("Online agent size={};cost={}", onlineAgentState.size(), (System.currentTimeMillis() - cost));
        if (onlineAgentState.isEmpty()) {
            return onlineServers;
        }
        List<AntActionStatus> agentList = new ArrayList<>(onlineAgentState.values());
        if (StringUtils.isNoneBlank(ip)) {
            agentList = onlineAgentState.values().stream().filter(x -> ip.equalsIgnoreCase(x.getIp())).collect(toList());
        }
        if (onlineAgentState.size() == 1) {
            BootServerView bootServerView = getBootServerView(authRestTemplate, agentList.get(0));
            if (bootServerView != null) {
                onlineServers.add(bootServerView);
            }
        } else {
            // 最大为CPU核数
            int nThreads = Math.min(agentList.size(), 16);
            log.debug("exec nThreads size:{};cost={}", nThreads, (System.currentTimeMillis() - cost));

            try (ParallelService<AntActionStatus, BootServerView> parallelService = new ParallelService<>(nThreads)) {
                parallelService.submit(agentList, input -> getBootServerView(authRestTemplate, input));
                onlineServers = parallelService.getResult(managerProperties.getTimeout(), TimeUnit.SECONDS);
            }
        }

        log.debug("Fetch online action boot status cost={}ms", (System.currentTimeMillis() - cost));
        log.trace("Fetch online action boot status=\r\n{}", onlineServers);

        return onlineServers;
    }

    /***
     * 指定服务的 引导启动状态（包含启动命令等）
     * @param ip
     * @param port
     * @param aid
     * @return
     * @throws IOException
     */
    public BootStatus getBootStatus(String ip, int port, String aid) throws IOException {
        log.debug("get.agent.boot.status:{}:{} {}", ip, port, aid);

        String url = String.format("http://%s:%d/skynet/agent/boot-status/%s", ip, port, aid);
        log.debug("the target url\t{}", url);
        return authRestTemplate.getForObject(url, BootStatus.class);
    }

    /**
     * 节点状态，
     *
     * @param ip
     * @param port
     * @param aid  为空时，agent节点本身的状态
     * @return
     * @throws IOException
     */
    public AntNodeState getNodeStatus(String ip, int port, String aid) throws IOException {
        log.debug("get.agent.node.status:{}:{} {}", ip, port, aid);

        String url = String.format("http://%s:%d/skynet/agent/node-status", ip, port);
        if (StringUtils.isNoneBlank(aid)) {
            url = String.format("http://%s:%d/skynet/agent/node-status/%s", ip, port, aid);
        }
        log.debug("the target url\t{}", url);
        return authRestTemplate.getForObject(url, AntNodeState.class);
    }
}

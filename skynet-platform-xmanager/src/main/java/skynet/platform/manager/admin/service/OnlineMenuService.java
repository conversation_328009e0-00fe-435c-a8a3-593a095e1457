package skynet.platform.manager.admin.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import skynet.boot.AppContext;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.common.repository.domain.AntMenuView;
import skynet.platform.feign.model.MenuView;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class OnlineMenuService implements AutoCloseable {

    private final IAntConfigService antConfigService;
    private final AppContext appContext;
    private final List<String> reportedMenuPathList = new ArrayList<>();

    public OnlineMenuService(IAntConfigService antConfigService, AppContext appContext) {
        this.antConfigService = antConfigService;
        this.appContext = appContext;
    }

    private String getOnlinePath() {
        return antConfigService.getSkynetOnlinePath() + "/menu";
    }

    public List<MenuView> getMenusList() {
        List<AntMenuView> antMenuViewList = antConfigService.getMenusList();
        return JSON.parseObject(JSON.toJSONString(antMenuViewList), new TypeReference<List<MenuView>>() {
        });
    }

    public String report(MenuView menuView) {

        AntMenuView antMenuView = JSON.parseObject(menuView.toJson(), AntMenuView.class);
        String path = antConfigService.reportMenu(antMenuView);
        reportedMenuPathList.add(path);
        return path;
    }

//    /**
//     * 汇报菜单
//     *
//     * @param resourceFile 相对于 src/main/resources/目录下的菜单资源文件名 如 /speech.ui.menu.json
//     * @throws IOException
//     */
//    public void report(String resourceFile) throws IOException {
//        if (antConfigService == null) {
//            return;
//        }
//        Assert.hasText(resourceFile), "resourceFile is blank");
//
//        Resource resource = new ClassPathResource(resourceFile);
//        log.debug("the resource {}; exists:{} ", resourceFile, resource.exists());
//
//        if (!resource.exists()) {
//            resource = new ClassPathResource(String.format("BOOT-INF/classes%s", resourceFile));
//        }
//        log.debug("the resource {}; exists:{} ", String.format("BOOT-INF/classes%s", resourceFile), resource.exists());
//
//        if (!resource.exists()) {
//            log.error("the resource is not exists.{}", resourceFile);
//            throw new IOException(String.format("the resource is not exists.[%s]", resourceFile));
//        }
//
//        // File resource = ResourceUtils.getFile(String.format("classpath:BOOT-INF/classes%s", resourceFile));
//
//        // 读取本地配置 汇报Menus
//        try (InputStream inputStream = resource.getInputStream()) {
//            String menuJson = IOUtils.toString(inputStream);
//            menuJson = menuJson.replaceAll("\\$\\{ipendport\\}", appContext.getIpEndpoint());
//            List<MenuView> menuViews = JSON.parseArray(menuJson, MenuView.class);
//            for (MenuView menuView : menuViews) {
//                this.report(menuView);
//            }
//        } catch (IOException e) {
//            log.error("report menus error", e);
//            throw e;
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    @Override
    public void close() throws Exception {
        if (antConfigService != null) {
            for (String path : reportedMenuPathList) {
                antConfigService.delData(path);
            }
        }
    }
}

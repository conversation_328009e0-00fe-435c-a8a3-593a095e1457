package skynet.platform.manager.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.platform.common.AppVersionBuilder;
import skynet.platform.common.condition.ConditionalOnManager;
import skynet.platform.manager.admin.service.RepoService;
import skynet.platform.manager.controller.ManagerEndpoint;
import skynet.platform.manager.security.AuthProperties;
import skynet.platform.manager.security.SkynetWebSecurityAutoConfiguration;

@Slf4j
@Configuration(proxyBeanMethods = false)
@AutoConfigureAfter(SkynetWebSecurityAutoConfiguration.class)
@ConditionalOnManager
public class ManagerAutoConfiguration {

    @Bean
    public ManagerEndpoint managerEndpoint(AppVersionBuilder appVersionBuilder, AuthProperties authProperties, RepoService repoService) {
        return new ManagerEndpoint(appVersionBuilder, authProperties, repoService);
    }
}

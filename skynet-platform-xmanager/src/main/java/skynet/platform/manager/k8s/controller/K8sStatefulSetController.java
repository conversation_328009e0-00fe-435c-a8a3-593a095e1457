package skynet.platform.manager.k8s.controller;

import java.util.List;

import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson2.JSONObject;

import lombok.extern.slf4j.Slf4j;
import skynet.boot.annotation.ExposeSwagger2;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.K8sUpdateImage;
import skynet.platform.feign.model.K8sUpdateReplica;
import skynet.platform.feign.model.K8sUpdateStatefulSetStrategy;
import skynet.platform.feign.model.SkynetApiResponse;
import skynet.platform.feign.service.V3K8sStatefulSet;
import skynet.platform.manager.audit.annotation.AuditLog;
import skynet.platform.manager.k8s.service.K8sStatefulSetService;

@Slf4j
@RestController
@ExposeSwagger2
public class K8sStatefulSetController implements V3K8sStatefulSet {
    
    private final K8sStatefulSetService k8sStatefulSetService;

    public K8sStatefulSetController(K8sStatefulSetService k8sStatefulSetService) {
        this.k8sStatefulSetService = k8sStatefulSetService;
    }

    @Override
    public SkynetApiResponse<List<JSONObject>> getStatefulSets(String ip, K8sQuery k8sQuery) throws Exception {
        SkynetApiResponse<List<JSONObject>> response = new SkynetApiResponse<>();
        List<JSONObject> results = k8sStatefulSetService.getStatefulSets(ip, k8sQuery);
        response.setData(results);
        log.debug("getStatefulSets response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<JSONObject> getStatefulSet(String ip, String namespace, String statefulSetName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sStatefulSetService.getStatefulSet(ip, namespace, statefulSetName);
        response.setData(result);
        log.debug("getStatefulSet response:{}", response);
        return response;
    }

    @Override
    public SkynetApiResponse<String> getStatefulSetYaml(String ip, String namespace, String statefulSetName) throws Exception {
        SkynetApiResponse<String> response = new SkynetApiResponse<>();
        String result = k8sStatefulSetService.getStatefulSetYaml(ip, namespace, statefulSetName);
        response.setData(result);
        log.debug("getStatefulSetYaml response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S StatefulSet 管理", operation = "删除 StatefulSet", message = "ip=#{#ip}, namespace=#{#namespace}, statefulSetName=#{#statefulSetName}")
    public SkynetApiResponse<JSONObject> deleteStatefulSet(String ip, String namespace, String statefulSetName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sStatefulSetService.deleteStatefulSet(ip, namespace, statefulSetName);
        response.setData(result);
        log.debug("deleteStatefulSet response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S StatefulSet 管理", operation = "重启 StatefulSet", message = "ip=#{#ip}, namespace=#{#namespace}, statefulSetName=#{#statefulSetName}")
    public SkynetApiResponse<JSONObject> restartStatefulSet(String ip, String namespace, String statefulSetName) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sStatefulSetService.restartStatefulSet(ip, namespace, statefulSetName);
        response.setData(result);
        log.debug("restartStatefulSet response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S StatefulSet 管理", operation = "更新副本数", message = "ip=#{#ip}, namespace=#{#namespace}, statefulSetName=#{#statefulSetName}, k8sUpdateReplica=#{#k8sUpdateReplica}")
    public SkynetApiResponse<JSONObject> updateStatefulSetReplicas(String ip, String namespace, String statefulSetName, K8sUpdateReplica k8sUpdateReplica) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sStatefulSetService.updateStatefulSetReplicas(ip, namespace, statefulSetName, k8sUpdateReplica);
        response.setData(result);
        log.debug("updateStatefulSetReplicas response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S StatefulSet 管理", operation = "调整镜像版本", message = "ip=#{#ip}, namespace=#{#namespace}, statefulSetName=#{#statefulSetName}, k8sUpdateImage=#{#k8sUpdateImage}")
    public SkynetApiResponse<JSONObject> updateStatefulSetImages(String ip, String namespace, String statefulSetName, K8sUpdateImage k8sUpdateImage) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sStatefulSetService.updateStatefulSetImages(ip, namespace, statefulSetName, k8sUpdateImage);
        response.setData(result);
        log.debug("updateStatefulSetImages response:{}", response);
        return response;
    }

    @Override
    @AuditLog(module = "K8S StatefulSet 管理", operation = "修改更新策略", message = "ip=#{#ip}, namespace=#{#namespace}, statefulSetName=#{#statefulSetName}, k8sUpdateStatefulSetStrategy=#{#k8sUpdateStatefulSetStrategy}")
    public SkynetApiResponse<JSONObject> updateStatefulSetStrategy(String ip, String namespace, String statefulSetName, K8sUpdateStatefulSetStrategy k8sUpdateStatefulSetStrategy) throws Exception {
        SkynetApiResponse<JSONObject> response = new SkynetApiResponse<>();
        JSONObject result = k8sStatefulSetService.updateStatefulSetStrategy(ip, namespace, statefulSetName, k8sUpdateStatefulSetStrategy);
        response.setData(result);
        log.debug("updateStatefulSetStrategy response:{}", response);
        return response;
    }
    
}

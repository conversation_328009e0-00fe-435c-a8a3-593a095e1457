package skynet.platform.manager.k8s.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;

import skynet.platform.feign.model.K8sQuery;

public interface K8sCustomObjectService {
    
    /**
     * 获取 CustomObject 列表
     */
    List<JSONObject> getCustomObjects(String ip, String group, String version, String plural, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 CustomObject 详情（namespace scope）
     */
    JSONObject getCustomObject(String ip, String namespace, String group, String version, String plural, String customObjectName) throws Exception;

    /**
     * 获取 CustomObject 详情（cluster scope）
     */
    JSONObject getCustomObject(String ip, String group, String version, String plural, String customObjectName) throws Exception;

    /**
     * 获取 CustomObject Yaml（namespace scope）
     */
    String getCustomObjectYaml(String ip, String namespace, String group, String version, String plural, String customObjectName) throws Exception;

    /**
     * 获取 CustomObject Yaml（cluster scope）
     */
    String getCustomObjectYaml(String ip, String group, String version, String plural, String customObjectName) throws Exception;
}

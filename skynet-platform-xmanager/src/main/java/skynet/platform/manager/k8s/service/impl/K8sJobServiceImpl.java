package skynet.platform.manager.k8s.service.impl;

import com.alibaba.fastjson2.JSONObject;
import io.kubernetes.client.openapi.apis.BatchV1Api;
import io.kubernetes.client.openapi.models.V1Job;
import io.kubernetes.client.openapi.models.V1JobList;
import io.kubernetes.client.openapi.models.V1Status;
import io.kubernetes.client.util.Yaml;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import skynet.platform.feign.model.K8sQuery;
import skynet.platform.manager.admin.v3.service.V3AgentService;
import skynet.platform.manager.k8s.service.K8sJobService;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class K8sJobServiceImpl extends K8sBaseService implements K8sJobService {

    public K8sJobServiceImpl(V3AgentService v3AgentService) {
        super(v3AgentService);
    }

    /**
     * 获取 Job 列表
     */
    @Override
    public List<JSONObject> getJobs(String ip, K8sQuery k8sQuery) throws Exception {

        BatchV1Api api = new BatchV1Api(initApiClient(ip));

        List<JSONObject> jobDtoList = new ArrayList<>();
        V1JobList jobList;
        if (StringUtils.isNotBlank(k8sQuery.getNamespace())) {
            jobList = api.listNamespacedJob(k8sQuery.getNamespace()).execute();
        } else {
            jobList = api.listJobForAllNamespaces().execute();
        }
        // 应用 K8sQuery 过滤条件
        List<V1Job> filteredJobs = applyK8sQueryFilter(jobList.getItems(), k8sQuery);

        for (V1Job job : filteredJobs) {
            jobDtoList.add(toJSON(job));
        }
        return jobDtoList;
    }

    /**
     * 获取 Job 详情
     */
    @Override
    public JSONObject getJob(String ip, String namespace, String jobName) throws Exception {

        BatchV1Api api = new BatchV1Api(initApiClient(ip));

        V1Job job = api.readNamespacedJob(jobName, namespace).execute();
        return toJSON(job);
    }

    /**
     * 获取 Job Yaml
     */
    @Override
    public String getJobYaml(String ip, String namespace, String jobName) throws Exception {

        BatchV1Api api = new BatchV1Api(initApiClient(ip));

        V1Job job = api.readNamespacedJob(jobName, namespace).execute();
        return Yaml.dump(job);
    }

    /**
     * 删除 Job
     */
    @Override
    public JSONObject deleteJob(String ip, String namespace, String jobName) throws Exception {

        BatchV1Api api = new BatchV1Api(initApiClient(ip));

        V1Status status = api.deleteNamespacedJob(jobName, namespace).execute();
        return toJSON(status);
    }

}

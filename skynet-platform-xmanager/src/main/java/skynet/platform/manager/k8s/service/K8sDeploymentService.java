package skynet.platform.manager.k8s.service;

import java.util.List;

import com.alibaba.fastjson2.JSONObject;

import skynet.platform.feign.model.K8sQuery;
import skynet.platform.feign.model.K8sRollbackDeployment;
import skynet.platform.feign.model.K8sUpdateDeploymentStrategy;
import skynet.platform.feign.model.K8sUpdateImage;
import skynet.platform.feign.model.K8sUpdateReplica;
import skynet.platform.feign.model.K8sUpdateRevisionHistoryLimit;

public interface K8sDeploymentService {
    
    /**
     * 获取 Deployment 列表
     */
    List<JSONObject> getDeployments(String ip, K8sQuery k8sQuery) throws Exception;

    /**
     * 获取 Deployment 详情
     */
    JSONObject getDeployment(String ip, String namespace, String deploymentName) throws Exception;

    /**
     * 获取 Deployment Yaml
     */
    String getDeploymentYaml(String ip, String namespace, String deploymentName) throws Exception;

    /**
     * 删除 Deployment
     */
    JSONObject deleteDeployment(String ip, String namespace, String deploymentName) throws Exception;

    /**
     * 重启 Deployment
     */
    JSONObject restartDeployment(String ip, String namespace, String deploymentName) throws Exception;

    /**
     * 伸缩
     */
    JSONObject updateDeploymentReplicas(String ip, String namespace, String deploymentName, K8sUpdateReplica k8sUpdateReplica) throws Exception;

    /**
     * 调整镜像版本
     */
    JSONObject updateDeploymentImages(String ip, String namespace, String deploymentName, K8sUpdateImage k8sUpdateImage) throws Exception;

    /**
     * 调整最大历史副本数
     */
    JSONObject updateDeploymentRevisionHistoryLimit(String ip, String namespace, String deploymentName, K8sUpdateRevisionHistoryLimit k8sUpdateRevisionHistoryLimit) throws Exception;

    /**
     * 回滚版本
     */
    JSONObject rollbackDeployment(String ip, String namespace, String deploymentName, K8sRollbackDeployment k8sRollbackDeployment) throws Exception;

    /**
     * 修改更新策略
     */
    JSONObject updateDeploymentStrategy(String ip, String namespace, String deploymentName, K8sUpdateDeploymentStrategy k8sUpdateDeploymentStrategy) throws Exception;

}

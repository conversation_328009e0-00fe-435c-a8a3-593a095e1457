package skynet.platform.manager.audit;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.StopWatch;
import skynet.platform.common.repository.config.IAntConfigService;
import skynet.platform.manager.audit.data.HistoryCatalog;
import skynet.platform.manager.audit.data.HistoryMessage;

import java.util.List;
import java.util.Map;

/**
 * 配置时光机
 * <p>
 * * TODO: 后续优化服务定义对象模型后再实现
 */
@Slf4j
//@Service
public class HistoryService {

    private final IAntConfigService antConfigService;

    public HistoryService(IAntConfigService antConfigService) {
        this.antConfigService = antConfigService;
    }


    public void save(HistoryMessage historyMessage) {
        String path = String.format(historyMessage.getCatalog().getZkPathTemplate(), antConfigService.getZkConfigService().getZkProperties().getClusterName());

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        List<String> allPath = antConfigService.getZkConfigService().getChildren(path);
        stopWatch.stop();
        log.error("{}; cost ={}", allPath.size(), stopWatch);

        StopWatch stopWatch2 = new StopWatch();
        stopWatch2.start();


        Map<String, String> map = antConfigService.getZkConfigService().exportData(path);
        stopWatch2.stop();
        log.error("{}; cost ={}", map.size(), stopWatch2);


        StopWatch stopWatch3 = new StopWatch();
        stopWatch3.start();

        Map<String, String> map2 = antConfigService.getZkConfigService().getChildrenWithData(path);
        stopWatch3.stop();
        log.error("{}; cost ={}", map2.size(), stopWatch3);

        log.debug("{}", map);
    }

    public List<HistoryMessage> getList(HistoryCatalog catalog) {
        return null;
    }


    public void delete(HistoryMessage historyMessage) {

    }


    /**
     * 回滚
     *
     * @param historyMessage
     */
    public void rollback(HistoryMessage historyMessage) {

    }


}

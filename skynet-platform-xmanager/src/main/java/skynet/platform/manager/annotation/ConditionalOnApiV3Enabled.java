package skynet.platform.manager.annotation;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;

import java.lang.annotation.*;

/**
 * Skynet API v3 Enabled 属性 条件 注解
 *
 * <pre>
 * property：
 * 缺省： skynet.api.v3.enabled=true
 *
 * </pre>
 *
 * <AUTHOR> [2020年10月26日14:09:49]
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
@Documented
//@ConditionalOnProperty(value = "skynet.api.v3.enabled", matchIfMissing = true)
@ConditionalOnExpression("'${skynet.ui.version:v3}'.equalsIgnoreCase('v3')")
public @interface ConditionalOnApiV3Enabled {
}

package skynet.platform.manager;

import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cloud.config.server.EnableConfigServer;
import org.springframework.context.annotation.ImportResource;
import org.springframework.scheduling.annotation.EnableAsync;
import skynet.boot.AppUtils;
import skynet.boot.annotation.*;
import skynet.platform.common.AppBootEnvironment;
import skynet.platform.manager.server.AntManagerApp;

/**
 * SKYNET Manager APP
 *
 * <pre>
 * </pre>
 *
 * <AUTHOR> [2018年8月10日 下午11:56:04]
 */
@ServletComponentScan
@EnableAsync
@EnableConfigServer
@EnableSkynetWebSocket
@EnableSkynetSecurity
@EnableSkynetZookeeper
@EnableSkynetSwagger2
@EnableSkynetDiscoveryClient
@EnableSkynetException
@ImportResource(locations = {"classpath*:spring/manager/*.xml"})
@SpringBootApplication
public class Bootstrap {

    /**
     * @param args 启动参数
     * @throws Exception Exception
     */
    public static void main(String[] args) throws Exception {
        AppBootEnvironment.init(AntManagerApp.ACTION_POINT);

        // 打印服务详情
        AppUtils.run(Bootstrap.class, args);
    }
}

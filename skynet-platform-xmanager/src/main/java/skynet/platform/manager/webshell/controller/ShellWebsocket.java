package skynet.platform.manager.webshell.controller;


import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;
import skynet.boot.websocket.HttpSessionConfigurator;
import skynet.platform.manager.webshell.core.LoginDataCache;
import skynet.platform.manager.webshell.core.WebShellService;
import skynet.platform.manager.webshell.vo.WebShellData;
import skynet.platform.manager.webshell.vo.WebShellLoginData;

import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * shell websocket
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnProperty(value = {"skynet.webshell.enabled"}, matchIfMissing = true)
@ServerEndpoint(value = "/skynet/api/shell/{sessionId}", configurator = HttpSessionConfigurator.class)
public class ShellWebsocket {

    private final WebShellService webShellService;
    private final LoginDataCache loginDataCache;

    private final Map<String, String> sessionIdMap = new ConcurrentHashMap<>();

    public ShellWebsocket(LoginDataCache loginDataCache, WebShellService webShellService) {
        this.loginDataCache = loginDataCache;
        this.webShellService = webShellService;
    }

    @OnOpen
    public void onOpen(@PathParam("sessionId") String sessionId, Session session) throws Exception {

        sessionIdMap.put(session.getId(), sessionId);
        // 根据sessionId 从 web session ShellSession；
        WebShellLoginData sshData = loginDataCache.get(sessionId);
        if (sshData != null) {
            webShellService.initConnection(session, sshData);
        } else {
            session.getBasicRemote().sendText("the current SessionId is not logged in.");
            session.close();
        }
    }

    @OnMessage
    public void onMessage(String message, Session session) throws IOException {
        loginDataCache.refresh(sessionIdMap.get(session.getId()));
        try {
            WebShellData shellData = JSON.parseObject(message, WebShellData.class);
            //调用service接收消息
            webShellService.receiveHandle(session, shellData);
        } catch (Exception e) {
            log.error("onMessage error ={}", e.getMessage());
            session.getBasicRemote().sendText("the server error: " + e.getMessage());
            throw e;
        }
    }

    @OnClose
    public void onClose(Session session, CloseReason closeReason) {
        log.debug("Session {} onClose because of {}", session.getId(), closeReason);
        try {
            //调用service关闭连接
            webShellService.close(session);
        } catch (Exception e) {
            log.error("close error:{}", e.getMessage());
        }
        if (sessionIdMap.containsKey(session.getId())) {
            sessionIdMap.remove(session.getId());
        }
    }

    @OnError
    public void onError(Session session, Throwable t) {
        log.debug("Session {} onError {}", session.getId(), t);
        try {
            //调用service关闭连接
            webShellService.close(session);
        } catch (Exception e) {
            log.error("close error:{}", e.getMessage());
        }
        if (sessionIdMap.containsKey(session.getId())) {
            sessionIdMap.remove(session.getId());
        }
    }
}

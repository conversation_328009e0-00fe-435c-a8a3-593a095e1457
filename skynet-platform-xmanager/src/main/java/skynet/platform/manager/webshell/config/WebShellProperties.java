/*
 * Copyright © 2020-present zmzhou-star. All Rights Reserved.
 */

package skynet.platform.manager.webshell.config;

import lombok.Getter;
import lombok.Setter;

/**
 * CORS(Cross Origin Resourse-Sharing) - 跨站资源共享
 * CSRF(Cross-Site Request Forgery) - 跨站请求伪造
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2021/5/11 16:28
 */
@Getter
@Setter
public class WebShellProperties {

    private boolean enabled = true;

    private boolean sftpEnabled = true;

    /**
     * 支持跨域请求的请求头信息字段
     */
    private String[] allowedHeaders = {"*"};
    /**
     * 支持跨域请求的方法
     */
    private String[] allowedMethods = {"POST", "GET", "PUT", "DELETE", "OPTIONS", "HEAD"};
    /**
     * 允许跨域请求的域名
     */
    private String[] allowedOriginPatterns = {"*"};
    /**
     * 是否允许发送Cookie
     */
    private boolean allowCredentials = true;
    /**
     * 本次请求的有效期
     */
    private long maxAge = 1800L;
}

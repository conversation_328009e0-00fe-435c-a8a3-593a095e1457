package skynet.platform.manager.exception;

import skynet.platform.feign.exception.ApiRequestErrorCode;
import skynet.platform.feign.exception.ApiRequestException;

/**
 * <AUTHOR>
 */
public class AgentNotRegisterException extends ApiRequestException {

    /**
     *
     */
    private static final long serialVersionUID = -89166241584953420L;

    private final String ip;

    public AgentNotRegisterException(String ip) {
        super(ApiRequestErrorCode.AGENT_NOT_REGISTER);
        this.ip = ip;
    }

    public String getIp() {
        return ip;
    }


    @Override
    public String getMessage() {
        return String.format("%s[IP=%s]", super.getMessage(), ip);
    }

}

<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.skynet</groupId>
        <artifactId>skynet-platform</artifactId>
        <version>${skynet-platform.vision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>skynet-platform-xmanager</artifactId>
    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <npm-registry>https://depend.iflytek.com/artifactory/api/npm/npm-repo/</npm-registry>
        <kubernetes-client-java.version>24.0.0</kubernetes-client-java.version>
        <jib-core.version>0.26.0</jib-core.version>
        <pty4j.version>0.13.5</pty4j.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.jetbrains.pty4j</groupId>
            <artifactId>pty4j</artifactId>
            <version>${pty4j.version}</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-platform-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.iflytek.skynet</groupId>
            <artifactId>skynet-platform-feign</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-openapi3-jakarta-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
        </dependency>
        <!-- kubernetes -->
        <dependency>
            <groupId>io.kubernetes</groupId>
            <artifactId>client-java</artifactId>
            <version>${kubernetes-client-java.version}</version>
        </dependency>
        <dependency>
            <groupId>io.kubernetes</groupId>
            <artifactId>client-java-extended</artifactId>
            <version>${kubernetes-client-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.cloud.tools</groupId>
            <artifactId>jib-core</artifactId>
            <version>${jib-core.version}</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>otf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ico</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
                <version>3.3.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>false</addClasspath>
                            <mainClass>skynet.platform.manager.Bootstrap</mainClass>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
        <pluginManagement>
            <plugins>
                <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself. -->
                <plugin>
                    <groupId>org.eclipse.m2e</groupId>
                    <artifactId>lifecycle-mapping</artifactId>
                    <version>1.0.0</version>
                    <configuration>
                        <lifecycleMappingMetadata>
                            <pluginExecutions>
                                <pluginExecution>
                                    <pluginExecutionFilter>
                                        <groupId>
                                            org.codehaus.mojo
                                        </groupId>
                                        <artifactId>
                                            exec-maven-plugin
                                        </artifactId>
                                        <versionRange>
                                            [1.6.0,)
                                        </versionRange>
                                        <goals>
                                            <goal>exec</goal>
                                        </goals>
                                    </pluginExecutionFilter>
                                    <action>
                                        <ignore/>
                                    </action>
                                </pluginExecution>
                            </pluginExecutions>
                        </lifecycleMappingMetadata>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <profiles>
        <profile>
            <id>ui</id>
            <activation>
                <activeByDefault>false</activeByDefault>
                <property>
                    <name>ui</name>
                    <value>true</value>
                </property>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.5.1</version>
                        <executions>
                            <execution>
                                <id>npm-install</id>
                                <phase>generate-sources</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>npm</executable>
                                    <arguments>
                                        <argument>--registry=${npm-registry}</argument>
                                        <argument>install</argument>
                                    </arguments>
                                    <workingDirectory>../skynet-platform-xmanager-ui/</workingDirectory>
                                </configuration>
                            </execution>
                            <execution>
                                <id>npm-run-build</id>
                                <phase>generate-sources</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>npm</executable>
                                    <arguments>
                                        <argument>run</argument>
                                        <argument>build:prod</argument>
                                    </arguments>
                                    <workingDirectory>../skynet-platform-xmanager-ui/</workingDirectory>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>

package skynet.ant;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.time.StopWatch;

import com.alibaba.fastjson2.TypeReference;

import skynet.boot.common.ParallelFunc;
import skynet.boot.common.ParallelService;
import skynet.boot.rpc.RpcClient;
import skynet.boot.rpc.domain.RpcRequest;
import skynet.boot.rpc.domain.RpcResponse;
import skynet.boot.core.domain.AntNodeState;

public class AntServerStateTest {
	public static void main(String[] args) throws Exception {

		StopWatch stopWatch = new StopWatch();

		stopWatch.reset();
		stopWatch.start();
		List<String> ipList = Arrays.asList("**************", "**************", "**************", "**************", "**************", "**************", "**************", "**************");

		try (ParallelService<String, AntServerState> parallelService = new ParallelService<String, AntServerState>()) {
			parallelService.submit(ipList, new ParallelFunc<String, AntServerState>() {
				@Override
				public AntServerState call(String input) throws Exception {

					try (RpcClient rpcClient = new RpcClient()) {
						rpcClient.connect(String.format("ws://%s:7230/", input));

						RpcResponse response = rpcClient.call(new RpcRequest("server"), 5000);
						AntServerState serverState = new AntServerState(response.getBody(AntNodeState.class));

						response = rpcClient.call(new RpcRequest("worker"), 5000);
						Map<String, AntWorkerState> stateT = response.getBody(new TypeReference<Map<String, AntWorkerState>>() {
						});
						for (AntWorkerState state : stateT.values()) {
							serverState.getWorkerStateList().add(state);
						}
						return serverState;
					}
				}
			});

			// 一直等待结束
			List<AntServerState> resultList = parallelService.getResult(5000, TimeUnit.MILLISECONDS);
			System.out.println(resultList.size());
		}

		System.err.println("all cost:\t" + stopWatch);

		stopWatch.reset();
		stopWatch.start();
		for (int i = 0; i < 50; i++) {
			skynet.boot.xmanager.test();
		}
		System.err.println("all cost:\t" + stopWatch);
	}

	public static void skynet.boot.xmanager.test() throws Exception {

		RpcClient rpcClient = new RpcClient();

		rpcClient.connect("ws://**************:7230/");
		RpcResponse response = rpcClient.call(new RpcRequest(), 5000);
		// String state = response.getBody(String.class);

		// System.out.println(state);

		Map<String, AntWorkerState> stateT = response.getBody(new TypeReference<Map<String, AntWorkerState>>() {
		});
		System.out.println(stateT.size());

		rpcClient.close();

	}
}

class AntWorkerState {
	public int index;
	public String action;
	public String id;
	public AntNodeState state;
}

class AntServerState {

	public AntServerState(AntNodeState serverState) {
		this.serverState = serverState;
		this.workerStateList = new ArrayList<AntWorkerState>();
	}

	private AntNodeState serverState;
	private List<AntWorkerState> workerStateList;

	/**
	 * @return the serverState
	 */
	public AntNodeState getServerState() {
		return serverState;
	}

	/**
	 * @return the workerStateList
	 */
	public List<AntWorkerState> getWorkerStateList() {
		return workerStateList;
	}
}

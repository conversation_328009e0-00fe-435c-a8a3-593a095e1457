<?xml version="1.0" encoding="utf-8"?>
<configuration>

    <jmxConfigurator/>
    <include resource="skynet/platform/logging/base.xml"/>

    <if condition='property("LOGSTASH_ENABLED").contains("true")'>
        <then>
            <logger name="ConsoleLogger" level="DEBUG" additivity="false">
                <appender-ref ref="SIFT_FILE"/>
                <appender-ref ref="LOGSTASH"/>
            </logger>
        </then>
        <else>
            <logger name="ConsoleLogger" level="DEBUG" additivity="false">
                <appender-ref ref="SIFT_FILE"/>
            </logger>
        </else>
    </if>


    <if condition='property("LOKI_ENABLED").contains("true")'>
        <then>
            <logger name="ConsoleLogger" level="DEBUG" additivity="false">
                <appender-ref ref="SIFT_FILE"/>
                <appender-ref ref="LOKI"/>
            </logger>
        </then>
        <else>
            <logger name="ConsoleLogger" level="DEBUG" additivity="false">
                <appender-ref ref="SIFT_FILE"/>
            </logger>
        </else>
    </if>
</configuration>

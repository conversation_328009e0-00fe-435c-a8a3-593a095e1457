package skynet.platform.agent.server;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.web.server.WebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.stereotype.Component;
import skynet.boot.AppContext;
import skynet.platform.common.AppBootEnvironment;

/**
 * AgentApp
 *
 * <AUTHOR> [2018年8月5日 下午10:25:41]
 */
@Slf4j
@Component
public class AntServerApp implements CommandLineRunner, WebServerFactoryCustomizer {
    public static final String ACTION_POINT = AppBootEnvironment.AGENT_ACTION_POINT;

    private final AppContext appContext;
    private final AntServerContext antServerContext;

    public AntServerApp(AppContext appContext, AntServerContext antServerContext) {
        this.appContext = appContext;
        this.antServerContext = antServerContext;
    }

    @Override
    public void run(String... args) throws Exception {
        log.info("The AntServerApp start...");
    }

    @SneakyThrows
    @Override
    public void customize(WebServerFactory factory) {
        log.info("The antServerContext start...");
        antServerContext.start(appContext);
    }
}
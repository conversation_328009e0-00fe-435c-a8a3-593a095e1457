package skynet.platform.agent.server.endpoint;

import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import skynet.platform.agent.server.AntServerContext;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@Configuration
public class NodeStateEndpoint {

    @Bean
    public BaseStatusEndPoint BaseStatusEndPoint(AntServerContext antServerContext) {
        return new BaseStatusEndPoint(antServerContext);
    }

    @Bean
    public DetailStatusEndPoint detailStatusEndPoint(AntServerContext antServerContext) {
        return new DetailStatusEndPoint(antServerContext);
    }

    @Endpoint(id = "skynet-agent-nodestatus")
    public static class BaseStatusEndPoint {

        private final AntServerContext antServerContext;

        public BaseStatusEndPoint(AntServerContext antServerContext) {
            this.antServerContext = antServerContext;
        }

        @ReadOperation
        public Object invoke() {
            return antServerContext.getAntNodeState();
        }
    }

    @Endpoint(id = "skynet-agent-detailstatus")
    public static class DetailStatusEndPoint {

        private final AntServerContext antServerContext;

        public DetailStatusEndPoint(AntServerContext antServerContext) {
            this.antServerContext = antServerContext;
        }

        @ReadOperation
        public Object invoke() throws IOException {
            return antServerContext.getStateDetail();
        }
    }
}
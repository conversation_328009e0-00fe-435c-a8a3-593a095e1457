package skynet.platform.agent.core.boot;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOCase;
import org.apache.commons.io.filefilter.WildcardFileFilter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import skynet.boot.common.FileZipUtil;
import skynet.platform.agent.core.exception.BootException;
import skynet.platform.common.domain.BootParam;

import java.io.File;
import java.io.FileFilter;
import java.io.InputStream;
import java.net.URLDecoder;
import java.util.*;
import java.util.jar.Attributes;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;
import java.util.jar.Manifest;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipFile;

/**
 * <AUTHOR>
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component(SpringBoot.BEAN_NAME)
public class SpringBoot extends JavaBoot {

    public static final String BEAN_NAME = "boot.springboot";

    private static final Pattern PATTERN_SPRING_BOOT = Pattern.compile("/spring-boot-[1|2].*.jar");
    private static final Pattern PATTERN_SPRING_BOOT_V2X = Pattern.compile("spring-boot-2.*.jar");
    private static final Pattern PATTERN_SPRING_BOOT_V1X = Pattern.compile("spring-boot-1.*.jar");
    private static final Pattern PATTERN_MANIFEST_MF = Pattern.compile("META-INF/MANIFEST.MF");
    private static final Map<SpringBootVersion, List<String>> CONTEXT_PATH_KEY_MAP;
    private static final String MANIFEST_FILE_KEY = "META-INF/MANIFEST.MF";

    private Map<String, Object> bootManifestMap = null;
    private SpringBootVersion springBootVersion;
    private String springBootFile = null;
    private File fatJar = null;

    static {
        CONTEXT_PATH_KEY_MAP = new HashMap<>(2);
        CONTEXT_PATH_KEY_MAP.put(SpringBootVersion.V1X, Arrays.asList("server.contextPath", "server.context-path"));
        CONTEXT_PATH_KEY_MAP.put(SpringBootVersion.V2X, Arrays.asList("server.servlet.contextPath", "server.servlet.context-path"));
    }

    public SpringBoot(Environment environment) {
        super(environment);
    }

    @Override
    public List<String> getWorkArgs() throws Exception {
        log.debug("get spring boot work args..");
        long start = System.currentTimeMillis();

        File fatJat = getFatJarFile();

        springBootVersion = getSpringBootVersion(fatJat);
        log.debug("[{}] depend SpringBoot version is {}.", fatJat, springBootVersion);

        // springBoot 2.x jar 采用 spring.config.additional-location
        String locationKey = springBootVersion == SpringBootVersion.V2X ? "spring.config.additional-location"
                : "spring.config.location";

        List<String> lines = super.getWorkArgs();
        // 除-D的所有参数
        for (int index = lines.size() - 1; index >= 0; index--) {
            if (lines.get(index).startsWith("-D")) {
                lines.remove(index);
            }
        }
        lines.add(String.format("--server.port=%d", this.getAppPort()));
        lines.add(String.format("--%s=%s", locationKey, StringUtils.join(getConfigFiles(), ",")));
        log.debug("get spring boot work args, cost={}", System.currentTimeMillis() - start);
        return lines;
    }

    protected List<String> getConfigFiles() {
        return Collections.singletonList(super.getAppConfigFile().toString());
    }


    @Override
    public Map<String, Object> getExtProps() {
        Map<String, Object> extMap = super.getExtProps();
        extMap.put("boot-jar", getFatJarFile() + "");
        extMap.put("spring-boot-version", springBootVersion);
        extMap.put("spring-boot-file", springBootFile);
        extMap.put("spring-boot-manifest", getManifestMap(getFatJarFile()));
        return extMap;
    }

    @Override
    protected List<String> getMain() throws Exception {
        File fatJar = getFatJarFile();
        List<String> objList = new ArrayList<>();
        objList.addAll(getJavaOpts());
        objList.add("-jar");
        objList.add(fatJar.getName());
        return objList;
    }

    protected File getFatJarFile() {
        if (fatJar == null) {
            BootParam bootParam = this.getAntActionParam().getBootParam();
            if (bootParam == null || StringUtils.isBlank(bootParam.getMainJar())) {
                throw new BootException("No config main jar.");
            }

            File workerHome = new File(getWorkHome());
            if (bootParam.getMainJar().endsWith(".jar") || bootParam.getMainJar().endsWith(".war")) {
                if (!workerHome.exists()) {
                    throw new BootException("the worker home [%s] not exists", workerHome);
                }
            } else {
                throw new BootException("the main jar [%s:%s] config error. not end with jar or war.",
                        workerHome, bootParam.getMainJar());
            }

            FileFilter fileFilter = new WildcardFileFilter(bootParam.getMainJar(), IOCase.INSENSITIVE);
            File[] bootJars = workerHome.listFiles(fileFilter);
            assert bootJars != null;
            if (bootJars.length == 0) {
                throw new BootException("No boot jar [%s] of the action [%s] were found in the [%s] directory",
                        bootParam.getMainJar(), this.getAntActionParam().getActionPoint(), workerHome);
            } else if (bootJars.length > 1) {
                throw new BootException("The  action [%s] multiple matching files [%s] were found in the [%s] directory",
                        this.getAntActionParam().getActionPoint(), bootParam.getMainJar(), workerHome);
            }
            fatJar = bootJars[0];
        }
        return fatJar;
    }

    @Override
    public String getServerContextPath() {
        List<String> keys = CONTEXT_PATH_KEY_MAP.getOrDefault(springBootVersion, Collections.EMPTY_LIST);
        for (String key : keys) {
            Object path = super.getAppEnvironment().get(key);
            if (path != null) {
                return path.toString();
            }
        }
        return null;
    }

    private SpringBootVersion getSpringBootVersion(File fatJat) throws Exception {
        List<String> temp = new ArrayList<>(0);
        try {
            temp = FileZipUtil.readFileNames(fatJat);
        } catch (Exception e) {
            String msg = String.format("Read SpringBootVersion from fatjar [%s] error:%s, please check that the jarfile is complete.", fatJat, e.getMessage());
            log.error(msg);
            throw new BootException(msg);
        }

        List<String> springBootJar = temp.parallelStream().filter(x -> PATTERN_SPRING_BOOT.matcher(x.toLowerCase()).find())
                .collect(Collectors.toList());

        if (springBootJar.size() > 0) {
            springBootFile = springBootJar.get(0);

            if (PATTERN_SPRING_BOOT_V1X.matcher(springBootJar.get(0)).find()) {
                return SpringBootVersion.V1X;
            }
            if (PATTERN_SPRING_BOOT_V2X.matcher(springBootJar.get(0)).find()) {
                return SpringBootVersion.V2X;
            }
        }

        List<String> manifestMfList = temp.parallelStream().filter(x -> PATTERN_MANIFEST_MF.matcher(x).find())
                .collect(Collectors.toList());

        if (manifestMfList.size() > 0) {
            try (ZipFile zipFile = new ZipFile(fatJat)) {
                try (InputStream zipInputStream = zipFile.getInputStream(zipFile.getEntry(manifestMfList.get(0)))) {
                    Manifest manifest = new Manifest(zipInputStream);
                    Attributes ma = manifest.getMainAttributes();
                    String springBootVersion = ma.getValue("Spring-Boot-Version");
                    if (null == springBootVersion) {
                        return SpringBootVersion.UNKNOWN;
                    } else if (springBootVersion.startsWith("1.")) {
                        return SpringBootVersion.V1X;
                    } else if (springBootVersion.startsWith("2.")) {
                        return SpringBootVersion.V2X;
                    } else {
                        log.warn("unknown spring boot version={}", springBootVersion);
                        return SpringBootVersion.UNKNOWN;
                    }
                }
            }
        } else {
            log.warn("can't find MANIFEST.MF file on path={}", fatJat);
            return SpringBootVersion.UNKNOWN;
        }
    }

    private Map<String, Object> getManifestMap(File fatJarFile) {
        try {
            if (bootManifestMap == null) {
                Manifest springBootFileManifest = getManifest(fatJarFile);
                if (springBootFileManifest != null) {
                    bootManifestMap = new TreeMap<>();
                    for (Map.Entry<Object, Object> item : springBootFileManifest.getMainAttributes().entrySet()) {
                        bootManifestMap.put(String.valueOf(item.getKey()).toLowerCase(), item.getValue());
                    }
                }
            }
        } catch (Exception e) {
            log.error("get manifest err:", e);
        }
        return bootManifestMap;
    }

    /**
     * 返回 .jar 文件中 Manifest 对象
     * 路径不存在、路径是文件夹、路径不是 zip 文件都会抛出异常
     *
     * @param jarFile jar file path
     * @return Manifest
     * @throws Exception ex
     */
    private static Manifest getManifest(File jarFile) throws Exception {
        if (!jarFile.exists()) {
            throw new Exception(String.format("file doesn't exist. %s", jarFile));
        }
        if (jarFile.isDirectory()) {
            throw new Exception(String.format("file is a directory. %s", jarFile));
        }
        try (JarFile jar = new JarFile(URLDecoder.decode(jarFile.toString(), "UTF-8"))) {
            Enumeration<JarEntry> entries = jar.entries();
            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                if (entry.getName().equals(MANIFEST_FILE_KEY)) {
                    return new Manifest(jar.getInputStream(entry));
                }
            }
        }
        return null;
    }


    enum SpringBootVersion {
        /**
         * springBoot 1.x
         */
        V1X,
        /**
         * springBoot 2.x
         */
        V2X,
        /**
         * unknown
         */
        UNKNOWN
    }

    //public static void main(String[] args) throws Exception {
//        getSpringBootVersion("/Users/<USER>/Downloads/dw-digit-manager.jar");
//        getSpringBootVersion("/Users/<USER>/Downloads/skynet-sample-stream-2.1.1009-SNAPSHOT.jar");
//        getSpringBootVersion("/Users/<USER>/Downloads/Z08服务器部署简述.docx");
//		SpringBoot springBoot = new SpringBoot();
//
//		SpringBootVersion sbv = springBoot.getSpringBootVersion(
//				new File("E:\\project\\turing\\ai-sp\\tuling-asrc\\tuling-asrc-boot\\target\\tuling-asrc-boot-2.1_1.0-SNAPSHOT.jar"));
//		System.out.print(sbv);
//
////		SpringBootVersion sbv = springBoot.getSpringBootVersion(
////				new File("/workspace/tuling/test_folder/jar_folder/tuling-ocr-service-2.1" + ".0-SNAPSHOT.jar"));
//		System.out.print(sbv);
//		sbv = springBoot.getSpringBootVersion(
//				new File("/workspace/tuling/test_folder/jar_folder/spring-configuration" + "-metadata.json"));
//		System.out.print(sbv);
//        StopWatch stopWatch = new StopWatch();
//        stopWatch.start();
//        for (int i = 0; i < 1; i++) {
//            File jarFile = new File("E:\\project\\skynet\\skynet-stream\\skynet-stream-samples\\skynet-sample-stream-handler\\target\\skynet-sample-stream-handler-3.0.0-SNAPSHOT.jar");
//            Manifest manifest = getManifest(jarFile);
//
//            if (manifest != null) {
//                Map<String, Object> bootManifestMap = new TreeMap<>();
//                for (Map.Entry<Object, Object> item : manifest.getMainAttributes().entrySet()) {
//                    bootManifestMap.put(String.valueOf(item.getKey()).toLowerCase(), item.getValue());
//                }
//                System.out.println(JSON.toJSONString(bootManifestMap));
//            }
//        }
//        stopWatch.stop();
//        System.out.println(stopWatch.toString());
//    }
}

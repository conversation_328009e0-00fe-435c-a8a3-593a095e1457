package skynet.platform.agent.core.health;

import skynet.platform.common.domain.BootStatus;

/**
 * 健康检查回调
 *
 * <AUTHOR> qq: 408365330
 */
public interface HealthListener {

    /**
     * 连接成功
     *
     * @throws Exception
     */
    void onConnected(BootStatus bootStatus) throws Exception;

    /**
     * 断开
     *
     * @throws Exception
     */
    void onClosed(BootStatus bootStatust) throws Exception;

}

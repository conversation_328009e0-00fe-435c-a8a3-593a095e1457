package skynet.platform.agent.core.controller;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import skynet.platform.agent.core.BootService;
import skynet.platform.agent.core.core.RunningEnvironment;
import skynet.platform.common.AppVersionBuilder;

import java.io.IOException;
import java.util.Date;
import java.util.Map;
import java.util.TreeMap;

/**
 * Index home
 *
 * <AUTHOR> [2018年10月15日 下午4:38:05]
 */
@Slf4j
@RestController
@RequestMapping(value = "/", produces = {MediaType.APPLICATION_JSON_VALUE})
@ConditionalOnProperty(value = "skynet.endpoint.index.enabled", matchIfMissing = true)
public class IndexController {

    private static final Date startTime = new Date();
    private final BootService bootService;
    private final RunningEnvironment runningEnvironment;
    private final AppVersionBuilder appVersionBuilder;

    public IndexController(BootService bootService, RunningEnvironment runningEnvironment, AppVersionBuilder appVersionBuilder) {
        this.bootService = bootService;
        this.runningEnvironment = runningEnvironment;
        this.appVersionBuilder = appVersionBuilder;
    }

    /**
     * 显示主页内容
     *
     * @return 系统信息
     */
    @GetMapping(value = "/", produces = {MediaType.APPLICATION_JSON_VALUE})
    public Map<String, Object> index() throws Exception {
        log.debug("index...");
        Map<String, Object> status = new TreeMap<>();
        status.put("app_name", "skynet-agent");
        status.put("start_time", DateFormatUtils.format(startTime, "yyyy-MM-dd HH:mm:ss"));
        try {
            status.put("boot_size", bootService.getAllBoot().size());
            //    status.put("app_version", appVersionBuilder.getManagerVersion());
        } catch (IOException e) {
            e.printStackTrace();
        }
        return status;
    }
}
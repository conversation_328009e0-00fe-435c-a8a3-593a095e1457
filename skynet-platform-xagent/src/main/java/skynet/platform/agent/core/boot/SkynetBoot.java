package skynet.platform.agent.core.boot;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOCase;
import org.apache.commons.io.filefilter.WildcardFileFilter;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import skynet.boot.SkynetProperties;
import skynet.platform.agent.core.exception.BootException;

import java.io.File;
import java.io.FileFilter;
import java.util.ArrayList;
import java.util.List;

/**
 * 后期将逐渐淘汰
 *
 * <pre>
 *  by 2019年06月22日10:16:25
 * </pre>
 *
 * <AUTHOR> [2019年6月22日 上午10:15:58]
 */
@Slf4j
@Scope(ConfigurableBeanFactory.SCOPE_PROTOTYPE)
@Component(SkynetBoot.BEAN_NAME)
public class SkynetBoot extends SpringBoot {

    public static final String BEAN_NAME = "boot.skynetboot";

    private final SkynetProperties skynetProperties;

    public SkynetBoot(Environment environment, SkynetProperties skynetProperties) {
        super(environment);
        this.skynetProperties = skynetProperties;
    }

    @Override
    protected List<String> getConfigFiles() {

        List<String> lines = new ArrayList<>();
        String applicationProperties = String.format("%s/plugin/%s/conf/application.properties", skynetProperties.getHome(), this.getBootParam().getPlugin());
        File applicationPropertiesFile = new File(applicationProperties);
        if (applicationPropertiesFile.exists()) {
            lines.add(applicationPropertiesFile.toString());
        }
        // 需要将 上级的放到最后，这样他的优先级最高
        lines.addAll(super.getConfigFiles());
        return lines;
    }

    @Override
    public String getSrcWorkHome() {
        return String.format("%s/plugin/%s/lib", skynetProperties.getHome(), this.getBootParam().getPlugin());
    }

    @Override
    protected File getFatJarFile() {
        log.debug("get skynet-boot fat jar ...");

        // 缺省是就是第一个 fat jar
        File workerHome = new File(getWorkHome());
        if (!workerHome.exists()) {
            throw new BootException("The worker home [%s] not exists", workerHome);
        }

        FileFilter fileFilter = new WildcardFileFilter("*.jar", IOCase.INSENSITIVE);
        File[] bootJars = workerHome.listFiles(fileFilter);
        assert bootJars != null;
        if (bootJars.length == 0) {
            throw new BootException("No boot jar of the action [%s] were found in the [%s] directory", this.getAntActionParam().getActionPoint(), workerHome);
        } else if (bootJars.length > 1) {
            throw new BootException("The  action [%s] multiple matching fatjar were found in the [%s] directory",
                    this.getAntActionParam().getActionPoint(), workerHome);
        }
        log.debug("skynet-boot fat jar={}", bootJars[0]);
        return bootJars[0];
    }
}

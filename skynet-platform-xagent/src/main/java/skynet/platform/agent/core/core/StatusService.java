package skynet.platform.agent.core.core;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONWriter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOCase;
import org.apache.commons.io.filefilter.WildcardFileFilter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import skynet.platform.common.domain.BootStatus;

import java.io.File;
import java.io.FileFilter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 状态存储服务
 *
 * <pre>
 * 利用本地文件系统。（内存文件系统）
 * </pre>
 *
 * <AUTHOR> [2018年7月9日 下午2:34:24]
 */
@Slf4j
@Service
public class StatusService {


    private final RunningEnvironment runningEnvironment;
    private final FileFilter statusFileFilter;

    public StatusService(RunningEnvironment runningEnvironment) {
        this.runningEnvironment = runningEnvironment;
        this.statusFileFilter = new WildcardFileFilter("*.json", IOCase.INSENSITIVE);
    }

    public synchronized void putBoot(BootStatus bootStatus) throws IOException {

        log.debug("putBoot bootStatus [aid={};up={}]", bootStatus.getAid(), bootStatus.getProfile().getUp());

        String s = JSON.toJSONString(bootStatus, JSONWriter.Feature.PrettyFormat);
        log.trace("putBoot bootStatus={}", bootStatus);

        File f = getStatusFile(bootStatus.getAid());
        FileUtils.writeByteArrayToFile(f, s.getBytes(StandardCharsets.UTF_8));
    }

    public synchronized void deleteBoot(String aid) {
        log.debug("DeleteBoot bootStatus [aid={}] begin..", aid);
        FileUtils.deleteQuietly(getStatusFile(aid));
        log.debug("DeleteBoot bootStatus [aid={}] end.", aid);
    }

    public List<BootStatus> getBoots() throws IOException {
        List<BootStatus> list = new ArrayList<>();
        File[] statusFiles = runningEnvironment.getActionDirectory().listFiles(statusFileFilter);
        assert statusFiles != null;
        for (File statusFile : statusFiles) {
            BootStatus bootStatus = getStatus(statusFile);
            if (bootStatus != null) {
                list.add(bootStatus);
            }
        }
        return list;
    }

    public BootStatus getBoot(String aid) throws IOException {
        Assert.hasText(aid, "The aid is blank.");
        File f = getStatusFile(aid.trim());
        return getStatus(f);
    }

    private BootStatus getStatus(File statusFile) throws IOException {
        BootStatus boot = null;
        if (statusFile.exists()) {
            String s = FileUtils.readFileToString(statusFile, StandardCharsets.UTF_8);
            if (StringUtils.isNotEmpty(s)) {
                try {
                    boot = JSON.parseObject(s, BootStatus.class);
                } catch (Exception e) {
                    log.error("GetStatus parseObject error: " + e.getMessage());
                    log.error("----------");
                    log.error(statusFile.getName());
                    log.error(s);
                    log.error("----------");
                }
            }
        }
        return boot;
    }

    private File getStatusFile(String aid) {
        return new File(runningEnvironment.getActionDirectory(), String.format("%s.json", aid));
    }
}

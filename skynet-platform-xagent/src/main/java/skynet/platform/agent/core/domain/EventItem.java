package skynet.platform.agent.core.domain;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.Getter;
import lombok.Setter;
import skynet.boot.common.domain.Jsonable;

import java.util.Date;

@Getter
@Setter
public class EventItem extends Jsonable {
    private String action;
    @JSONField(format = "MM-dd HH:mm:ss")
    private Date timestamp;
    private String node;
    private String hostname;
    private String address;
    private String app;
    private long pid;
    private String desc;

    public EventItem() {
        this.timestamp = new Date();
    }

    @Override
    public String toString() {
        return super.toString();
    }
}

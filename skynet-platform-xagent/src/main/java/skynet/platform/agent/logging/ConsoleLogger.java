package skynet.platform.agent.logging;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.core.env.Environment;
import skynet.platform.common.logging.ConsoleLoggerFilter;

/**
 * <AUTHOR>
 * @date 2020/5/12 20:31
 */
public class ConsoleLogger {

    private static final Logger ConsoleLogger = LoggerFactory.getLogger("ConsoleLogger");

    public ConsoleLogger(Environment environment, String actionId, boolean isLogCollection) {
        // actionId 需要与 skynet-platform-common/src/main/resources/skynet/platform/logging/sifting-appender.xml  文件中配置保持一致
        MDC.put("actionId", actionId);
        this.setPid(0);
        if (!isLogCollection) {
            MDC.put(ConsoleLoggerFilter.LOG_COLLECT_DISABLED_KEY, "true");
        }
    }

    public void setPid(long pid) {
        MDC.put("pid", String.valueOf(pid));
    }

    public void info(String msg) {
        ConsoleLogger.info(msg);
    }

    public void debug(String msg) {
        ConsoleLogger.debug(msg);
    }
}

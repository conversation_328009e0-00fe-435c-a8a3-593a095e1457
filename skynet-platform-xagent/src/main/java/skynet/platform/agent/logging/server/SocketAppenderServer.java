package skynet.platform.agent.logging.server;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.extern.slf4j.Slf4j;
import skynet.platform.agent.logging.appender.SkynetLoggerAppender;
import skynet.platform.agent.logging.config.SocketProperties;

import javax.net.ServerSocketFactory;
import java.io.IOException;
import java.net.ServerSocket;
import java.net.Socket;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * <AUTHOR>
 */
@Slf4j
public class SocketAppenderServer {

    private final SocketProperties socketProperties;
    private final List<SocketReceiver> socketReceiverList = new ArrayList<>();
    private final List<SkynetLoggerAppender> appenderList;

    private boolean closed = false;
    private ServerSocket serverSocket;
    private CountDownLatch latch;

    public SocketAppenderServer(SocketProperties socketProperties, List<SkynetLoggerAppender> appenderList) {
        this.socketProperties = socketProperties;
        this.appenderList = appenderList;
        Thread receiveThread = new Thread(this::start);
        receiveThread.setName("socket-appender-log-server");
        receiveThread.start();
    }

    private void start() {
        this.appenderList.forEach(x -> x.getAppender().start());
        final String oldThreadName = Thread.currentThread().getName();
        try {
            final String newThreadName = getServerThreadName();
            Thread.currentThread().setName(newThreadName);
            log.info("SocketAppenderServer start ..,[LoggerAppender List size = {}]", appenderList.size());
            log.info("Listening on port={}", socketProperties.getPort());
            serverSocket = getServerSocketFactory().createServerSocket(socketProperties.getPort());
            while (!closed) {
                log.debug("Waiting to accept a new client.");
                signalAlmostReadiness();
                Socket socket = serverSocket.accept();
                log.debug("Connected to client at {} Starting new socket node.", socket.getInetAddress());
                SocketReceiver newSocketReceiver = new SocketReceiver(this, socket, this.appenderList);
                synchronized (socketReceiverList) {
                    socketReceiverList.add(newSocketReceiver);
                }
                final String clientThreadName = getClientThreadName(socket);
                new Thread(newSocketReceiver, clientThreadName).start();
            }
        } catch (Exception e) {
            if (closed) {
                log.info("Exception in run method for a closed server. This is normal.");
            } else {
                log.error("Unexpected failure in run method", e);
            }
        } finally {
            Thread.currentThread().setName(oldThreadName);
        }
    }

    /**
     * Returns the name given to the server thread.
     */
    protected String getServerThreadName() {
        return String.format("Logback %s (port %d)", getClass().getSimpleName(), socketProperties.getPort());
    }

    /**
     * Returns a name to identify each client thread.
     */
    protected String getClientThreadName(Socket socket) {
        return String.format("Logback SocketNode (client: %s)", socket.getRemoteSocketAddress());
    }

    /**
     * Gets the platform default {@link ServerSocketFactory}.
     * <p>
     * Subclasses may override to provide a custom server socket factory.
     */
    protected ServerSocketFactory getServerSocketFactory() {
        return ServerSocketFactory.getDefault();
    }

    /**
     * Signal another thread that we have established a connection This is useful for testing purposes.
     */
    void signalAlmostReadiness() {
        if (latch != null && latch.getCount() != 0) {
            latch.countDown();
        }
    }

    /**
     * Used for testing purposes
     *
     * @param latch
     */
    void setLatch(CountDownLatch latch) {
        this.latch = latch;
    }

    /**
     * Used for testing purposes
     */
    @JSONField(serialize = false)
    public CountDownLatch getLatch() {
        return latch;
    }

    public boolean isClosed() {
        return closed;
    }

    public void close() {
        closed = true;
        if (serverSocket != null) {
            try {
                serverSocket.close();
            } catch (IOException e) {
                log.error("Failed to close serverSocket", e);
            } finally {
                serverSocket = null;
            }
        }

        log.info("closing this server");
        synchronized (socketReceiverList) {
            for (SocketReceiver sn : socketReceiverList) {
                sn.close();
            }
        }
        if (socketReceiverList.size() != 0) {
            log.warn("Was expecting a 0-sized socketNodeList after server shutdown");
        }
        this.appenderList.forEach(x -> x.getAppender().stop());
    }

    public void socketNodeClosing(SocketReceiver sn) {
        log.debug("Removing {}", sn);

        // don't allow simultaneous access to the socketNodeList
        // (e.g. removal whole iterating on the list causes
        // java.util.ConcurrentModificationException
        synchronized (socketReceiverList) {
            socketReceiverList.remove(sn);
        }
    }
}

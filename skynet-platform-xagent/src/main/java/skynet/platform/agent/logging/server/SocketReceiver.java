package skynet.platform.agent.logging.server;

import ch.qos.logback.classic.AsyncAppender;
import ch.qos.logback.classic.spi.ILoggingEvent;
import lombok.extern.slf4j.Slf4j;
import skynet.platform.agent.logging.appender.SkynetLoggerAppender;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.net.Socket;
import java.net.SocketAddress;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class SocketReceiver implements Runnable {

    private final Socket socket;
    private final SocketAddress remoteSocketAddress;
    private final SocketAppenderServer socketAppenderServer;
    private final List<SkynetLoggerAppender> appenderList;

    private ObjectInputStream ois;
    private boolean closed = false;

    public SocketReceiver(SocketAppenderServer socketAppenderServer, Socket socket, List<SkynetLoggerAppender> appenderList) {
        this.socketAppenderServer = socketAppenderServer;
        this.socket = socket;
        this.remoteSocketAddress = socket.getRemoteSocketAddress();
        this.appenderList = appenderList;
    }

    @Override
    public void run() {
        try {
            ois = new ObjectInputStream(new BufferedInputStream(socket.getInputStream()));
        } catch (Exception e) {
            log.error("Could not open ObjectInputStream to " + socket, e);
            closed = true;
        }
        try {
            if (appenderList.size() > 0) {
                ILoggingEvent event;
                AsyncAppender asyncAppender = new AsyncAppender();
                appenderList.forEach(x -> asyncAppender.addAppender(x.getAppender()));
                asyncAppender.start();
                while (!closed) {
                    // read an event from the wire
                    event = (ILoggingEvent) ois.readObject();
                    log.debug("Socket Receive Log data={}", event);
                    asyncAppender.doAppend(event);
                }
                asyncAppender.stop();
            } else {
                while (!closed) {
                    ois.readObject();
                }
            }
        } catch (java.io.EOFException e) {
            log.error("Caught java.io.EOFException closing connection.");
        } catch (java.net.SocketException e) {
            log.error("Caught java.net.SocketException closing connection.");
        } catch (IOException e) {
            log.error("Caught java.io.IOException: " + e);
            log.error("Closing connection.");
        } catch (Exception e) {
            log.error("Unexpected exception. Closing connection.", e);
        }

        socketAppenderServer.socketNodeClosing(this);
        close();
    }

    public void close() {
        if (closed) {
            return;
        }
        closed = true;
        if (ois != null) {
            try {
                ois.close();
            } catch (IOException e) {
                log.warn("Could not close connection.", e);
            } finally {
                ois = null;
            }
        }
    }

    @Override
    public String toString() {
        return this.getClass().getName() + remoteSocketAddress.toString();
    }
}

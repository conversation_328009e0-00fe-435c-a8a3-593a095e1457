package skynet.platform.agent.logging.web;

import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;
import skynet.boot.websocket.HttpSessionConfigurator;
import skynet.platform.common.logging.BaseLogWebsocket;

import jakarta.websocket.server.ServerEndpoint;

/**
 * Log日志实时推送服务
 *
 * <pre>
 * // ws://127.0.0.1:8080/skynet/agent/log/ws/mq-vspp-v1_0@engine_1
 * </pre>
 *
 * <AUTHOR> [2017年12月1日 上午11:12:33]
 */
@Component
@ServerEndpoint(value = "/skynet/agent/log/ws/{actionId}", configurator = HttpSessionConfigurator.class)
public class LogWebsocket extends BaseLogWebsocket {

    public LogWebsocket(Environment environment) {
        super(environment);
    }
}

FROM artifacts.iflytek.com/hy-docker-private/skynet/alpine:3.20.2

WORKDIR /iflytek/server

RUN echo "https://mirrors.aliyun.com/alpine/v3.19/main/" > /etc/apk/repositories && \
    echo "https://mirrors.aliyun.com/alpine/v3.19/community/" >> /etc/apk/repositories

# 定义时区参数
ENV TZ=Asia/Shanghai

# 设置时区
RUN apk add --no-cache tzdata && \
    ln -sf /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo "$TZ" > /etc/timezone

# 添加字体
RUN apk add --update font-adobe-100dpi ttf-dejavu fontconfig
RUN apk --no-cache add curl zip
RUN apk add --no-cache openjdk21

# 安装 Python 3 和 pip
RUN apk add --no-cache python3 py3-pip

# 验证 Python 3 安装
RUN python3 --version

# 安装 BusyBox
RUN apk add --no-cache busybox

# 验证 BusyBox
RUN busybox --help
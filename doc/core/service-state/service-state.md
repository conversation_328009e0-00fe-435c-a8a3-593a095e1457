<h1>服务状态查看</h1>
<!-- TOC -->

- [[1] 进程信息](#1-进程信息)
- [[2] 查看日志](#2-查看日志)
- [[3] 查看配置](#3-查看配置)
- [[4] SpringBoot Endpoints](#4-springboot-endpoints)
- [[5] 标准输出](#5-标准输出)
- [[6] 健康日志](#6-健康日志)
- [[7] 服务监控](#7-服务监控)

<!-- /TOC -->

![alt](res/panel.png)

上图是服务状态查看面板的主要功能区，下面按照编号进行介绍

# [1] 进程信息

进程号、进程启动时间、进程运行时长。

# [2] 查看日志

对于不同的服务类型，日志的位置不同  
*  SpringBoot/SkynetBoot: 日志位置为${SKYNET_HOME}/log/${SKYNET_ACTION_ID}.log
* BaseBoot/DockerBoot: 日志位置依赖于 服务定义`'基本信息'`中的`'日志文件'`配置。

目前仅支持单个日志的查看。日志显示界面会随着日志的更新不断向下滚动，如果希望暂停滚动，可以点击`'暂停跟踪'`。

![alt](res/log-view.png)

# [3] 查看配置

查看配置，返回的响应内容是skynet根据`"集群-系统-服务"`三级配置的规则最终生成的配置项列表。

# [4] SpringBoot Endpoints

该区域是所有能够通过web访问的 SpringBoot Endpoint。只有当服务类型是SpringBoot或者是SkynetBoot时，该区域才会显示。

# [5] 标准输出

从2.1.1009.1开始，服务的标准错误输出被重定向到了标准输出，都可以从该功能区域查看。

![alt](res/stdout-view.png)

标准输出会不断向下滚动，如果希望暂停滚动，可以点击`'暂停跟踪'`。右上角有个图标，点击可以在新窗口打开标准输出，扩大显示面积。

# [6] 健康日志

健康日志是skynet对服务进行状态检查的输出

# [7] 服务监控

该功能依赖于`'仪表盘`'服务的部署。






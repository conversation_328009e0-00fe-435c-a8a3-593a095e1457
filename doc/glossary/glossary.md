<h1>术语表</h1>

* `skynet-xagent`:  
每个物理服务器节点上都必须部署一个的进程，负责启动被托管服务、停止被托管服务、检查被托管服务健康状态、采集监控指标等等职责。（如果一个物理节点属于两个不同的skynet cluster，那么它运行的skynet-xagent进程就有两个，同一个xagent不能归属不同cluster）。在较老版本的skynet中也表述为`'AntServer'`

* `skynet-xmanager`:  
整个skynet集群唯一的服务进程，提供了skynet运维的门户UI，同时集成了资源仓库服务接口。

* `注册服务器`：  
实际上就是注册一个skynet-xagent，它包括两个过程：1）登记ssh登陆信息和备注 2）分发skynet程序包到目标主机

* `系统编码`：  
一个应用系统是一组具有部分共性的服务的抽象集合，系统编码是一个应用系统的唯一ID，可以使用${SKYNET_PLUGIN_CODE}在服务定义的其他输入中进行引用。

* `服务编码(Action Code)`：  
同一个应用的下不同服务的服务编码必须不同，但是不同应用下的服务服务编码可以相同。服务编码在服务定义的基本信息tab页进行输入，可以使用${SKYNET_ACTION_CODE}在服务定义的其他输入中进行引用。

* `服务坐标(Action Point)`：  
相当于服务的唯一ID，服务坐标由系统自动生成，生成规则：`服务坐标=服务编码@系统编码`，可以在服务定义的输入中用${SKYNET_ACTION_POINT}进行引用。

* `服务ID(Action ID)`:
是服务实例的唯一ID。当某个节点上只部署一个服务实例时，`服务ID`=`服务坐标`; 当某个节点上部署服务的多个实例，`服务ID`=`服务坐标_序号`。  
例如，当服务坐标为 mq-consumer@demo 的服务在一个节点上部署3个实例，那么这三个服务实例的ID分别为 mq-consumer@demo_1 ,  mq-consumer@demo_2,  mq-consumer@demo_3。


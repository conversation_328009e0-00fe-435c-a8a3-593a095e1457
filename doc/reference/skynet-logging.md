
# Skynet-Logging日志配置说明

[TOC]

## 1. 日志功能介绍

Skynet日志模块，是通过实现托管服务日志的统一收集，达到日志进行本地持久化、或者输送到第三方日志平台的目的。本文主要是介绍Skynet运行时涉及的相关的日志处理流程和配置说明，以便了解Skynet日志处理思路、并能够对日志的常用属性进行配置。

Skynet日志模块特点如下：

- Skynet配置主要包括**manager日志、agent日志以及服务日志**；

- 日志配置可以通过Skynet管理页面直接进行配置，无需的登录服务器进行更改；

- 支持**集群级、系统级、服务级**三级日志配置，其优先级为**服务级>系统级>集群级**；

- 所有日志输出，均可以在管理界面直接查看，并能实时查看最新日志，无需登录服务器查看日志；

- 主要日志组件为Logback + ElasticSearch + <PERSON><PERSON>，和主流日志解决方案一致。

  

## 2. 日志架构

日志架构介绍，主要是向大家简单介绍Skynet日志模块的实现方式，方便大家理解日志的配置，这部分内容简单了解即可，不会影响大家对于日志模块的使用。

### 2.1 manager日志

![manager](./img/manager.png)

#### 2.1.1 日志类型

Skynet管理页面xmanager日志主要包括3种日志：自身日志、审计日志和分发日志，下面是这三种日志介绍

- **自身日志**：xmanager启动默认日志，包括所有日志信息，日志名称为 *<EMAIL>*。xmanager启动过程中如果遇到什么问题，可以通过此日志文件进行排查。

- **审计日志**：记录平台登录信息，日志名称为 *<EMAIL>*，以及在平台上进行增加、修改、删除等操作的日志。通过该日志可以查看用户在平台上进行的所有操作信息。

  ![审计日志](./img/审计日志.png)

- **分发日志**：记录管理平台对于服务器的分发信息，名称名称为 *agent_deploy_{服务器IP}.log*。主要包括部署包的分发处理过程，以及docker环境安装过程等。

  ![分发日志](./img/分发日志.png)

#### 2.1.2 架构说明

1. xmanager通过RollingFileAppender以及SiftingAppender将日志存到本地文件；
2. 通过参数开关，判断是否将日志数据向logstash发送。向logstash发送的日志数据，可以配合ElassticSearch和Kibana使用。

### 2.2 agent日志

![](./img/agent.png)

#### 2.2.1 日志类型

agent日志是指注册的节点操作日志以及节点上服务产生的日志，主要包括两种日志类型：agent节点日志、节点上托管的服务日志

- **agent节点日志**： 日志名称为 *<EMAIL>*，主要记录的是agent节点的信息或者对agent节点操作的日志记录；
- **托管服务日志**：托管服务日志，日志名称为 *{托管服务ID}_console.log*，记录服务的启动信息以及日常运行信息。

#### 2.2.2 架构说明

1. 在 Skynet上面托管的服务，借助服务的控制台日志 输入到操作系统的FIFO 文件，Skynet-agent 同步实话读取，并通过 Logback的 *SiftingAppender* 向发送日志发送日志；
2. 托管的Java类型的日志，也可以通过选择 配置 logback 或log4j中的 *SocketAppender*，来向skynet-agent发送日志，让agent统计采集输送到logstash中；这个与托管服务控制台采集日志的区别，是可以将sl4j日志组件的上下文数据 *对象化* 的存储到ES，如行号，文件，日志级别。
3. skynet-agent和skynet-xmanager类似，通过 Logback 的 *RollingFileAppender* 组件将日志文件存储到本地，通过 *LogStashTcpSocketAppender* 将日志文件发给 logstash（是否发送为可选的，通过Skynet.logging.logstash.enabled属性进行配置）。



## 3. 日志配置

本节介绍Skynet的日志配置信息，讲述一些日志关键信息如何进行配置以及查看。

### 3.1 公共配置

#### 3.1.1 logstash配置 

下面是日志采集是否启用，以及日志采集地址配置

```properties
# logstash 配置（以下是默认值）
skynet.logging.logstash.enabled=false
skynet.logging.logstash.host=127.0.0.1:19601
```

| 属性字段                        | 字段说明                                                     | 默认值              |
| :------------------------------ | ------------------------------------------------------------ | ------------------- |
| skynet.logging.logstash.enabled | 是否开启日志采集                                             | false（代码里默认） |
| skynet.logging.logstash.host    | 日志采集的logstash地址，可以用Skynet的 **[基础平台-日志]** 的 **log-logstash服务** | 127.0.0.1:19601     |

补充说明：

1. 如果想要使用日志收集模块，需要导入日志模块zk配置，文件名为“ant-log.zk.config”；

2. 导入日志的zk配置后，可以在集群级里面看到上述配置，而且Skynet.logging.logstash.enabled属性会变为true；

3. 服务节点需要日志采集时，可以在服务定义页面的功能选项一栏，将启用日志收集勾选上，该服务的日志就会进行采集；

   - 该服务节点需要使用了Skynet依赖，或者实现了SocketAppender（配置可以参考下文）
   - 使用日志采集功能需要使用Skynet的日志组件，并分配**“基础平台-日志”**的**log-logstash服务**，最好将**log-es-node1**以及**log-kibana**也启动
   
![日志采集启动](./img/日志采集启动.png)


#### 3.1.2 日志文件说明

本地日志生成采用了 *SizeAndTimeBasedRollingPolicy* 和 *RollingFileAppender* 策略。

下面是常用默认配置

```properties
log.file.clean.history.on.start=false
rolling.file.name.pattern=${LOG_PATH}/XXXX.%d{yyyy-MM-dd}.%i.gz}
log.file.max.size=20MB
log.file.max.history=7
```

| 属性字段                        | 字段说明                                                     | 默认值                                 |
| ------------------------------- | ------------------------------------------------------------ | -------------------------------------- |
| log.file.clean.history.on.start | 配置应用程序启动时，是否进行强制进行日志归档清理             | false                                  |
| rolling.file.name.pattern       | 生成的滚动日志文件名，默认为日志名+日期+序号，滚动日志会进行压缩 | ${LOG_PATH}/XXXX.%d{yyyy-MM-dd}.%i.gz} |
| log.file.max.size               | 文件的最大值                                                 | 20MB                                   |
| log.file.max.history            | 日志的最长存储时间，默认为7天                                | 7                                      |

更多属性，请参考：https://docs.spring.io/spring-boot/docs/current/reference/htmlsingle/#boot-features-logging。

### 3.2 agent日志配置

#### 3.2.1 agent本身日志

下面是agent节点日志配置示例

```properties
# 文件路径：
logging.file.name=/iflytek/server/skynet/log/<EMAIL>

logging.level.skynet=ERROR
logging.level.skynet.boot=INFO
logging.pattern.console=%d{MM-dd HH:mm:ss.SSS} %clr(%5p) %clr([%9t]){faint} %clr(%-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}]){cyan}%clr(:){faint} %m%n
logging.pattern.file=%d{MM-dd HH:mm:ss.SSS} -%5p 11245[%t] %-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}]: %m%n

```

| 属性字段                  | 字段说明                                        | 默认值                                                       |
| ------------------------- | ----------------------------------------------- | ------------------------------------------------------------ |
| logging.file.name         | agent日志名称配置，一般情况下不用更改           | <EMAIL>                                           |
| logging.level.Skynet      | Skynet.*命名空间日志级别                        | ERROR                                                        |
| logging.level.Skynet.boot | Skynet.boot*命名空间日志级别                    | INFO                                                         |
| logging.pattern.console   | console类型日志，每行日志输出格式，一般不作修改 | %d{MM-dd HH:mm:ss.SSS} %clr(%5p) %clr([%9t]){faint} %clr(%-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}]){cyan}%clr(:){faint} %m%n |
| logging.pattern.file      | file类型日志，每行日志输出格式，一般不作修改    | %d{MM-dd HH:mm:ss.SSS} -%5p 11245[%t] %-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}]: %m%n |

agent日志输出位置，默认在/iflytek/server/Skynet/log/下面。如果想要对输出位置进行修改，可以通过修改ant基础服务的插件日志配置，修改属性如下：

```
logging.file.path=../log
```

#### 3.2.2 托管服务日志

托管服务的日志配置属性如下

```properties
# 只向控制台输出（因为：agent会采集所有控制台的日志到本地，避免重复）
logging.config=file:/iflytek/server/Skynet/conf/skynet-logback.xml
# spring-boot-1.5x
logging.file=/iflytek/server/Skynet/log/<EMAIL>
# spring-boot-2.x
logging.file.name=/iflytek/server/Skynet/log/<EMAIL>

logging.pattern.console=%d{MM-dd HH:mm:ss.SSS} %clr(%5p) %clr([%9t]){faint} %clr(%-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}]){cyan}%clr(:){faint} %m%n
logging.pattern.file=%d{MM-dd HH:mm:ss.SSS} -%5p 11245[%t] %-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}]: %m%n
```

| 属性字段                | 字段说明                                                     | 默认值                                                       |
| ----------------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| logging.config          | 服务日志采用的logback配置，配置里面只是配置了一些日志级别，并没有本地文件输出配置。这是因为本地日志输出与agent控制，所以本配置一般不用进行更改。 | file:/iflytek/server/Skynet/conf/Skynet-logback.xml          |
| logging.file            | spring-boot-1.5x版本的日志文件名称配置                       |                                                              |
| logging.file.name       | spring-boot-1.5x版本的日志文件名称配置                       |                                                              |
| logging.pattern.console | console类型日志，每行日志输出格式，一般不作修改              | %d{MM-dd HH:mm:ss.SSS} %clr(%5p) %clr([%9t]){faint} %clr(%-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}]){cyan}%clr(:){faint} %m%n |
| logging.pattern.file    | file类型日志，每行日志输出格式，一般不作修改                 | %d{MM-dd HH:mm:ss.SSS} -%5p 11245[%t] %-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}]: %m%n |

**输出日志名称规则**：*${logging.file.path}/${actionId}_console.log*，如log-logstach-xpack@ant-log_console.log。

日志级别配置，在对应的服务定义中进行配置。

![服务日志级别配置](./img/服务日志级别配置.png) 
 
**SocketAppender日志**：针对java SocketAppender输入的日志

```properties
# socket-appender-server 配置（以下是默认值）
skynet.logging.socket.enabled=true
skynet.logging.socket.host=127.0.0.1
skynet.logging.socket.port=4560
```

| 属性字段                      | 字段说明           | 默认值    |
| ----------------------------- | ------------------ | --------- |
| skynet.logging.socket.enabled | 是否开始socket参数 | true      |
| skynet.logging.socket.host    | socker的ip地址     | 127.0.0.1 |
| skynet.logging.socket.port    | socker所用端口     | 4560      |

#### 3.2.3 第三方服务集成配置

托管在Skynet的第三方服务，如果想要使用Skynet的日志收集服务，从而使用Skynet的日志解决方案。需要服务本身支持logback的SocketAppender模式，下面是SocketAppender一般配置，配置文件名一般为logback-spring.xml

```xml
    <appender name="SOCKET" class="ch.qos.logback.classic.net.SocketAppender">
        <RemoteHost>${skynet.logging.socket.appender.host:-127.0.0.1}</RemoteHost>
        <Port>${skynet.logging.socket.appender.port:-4560}</Port>
        <ReconnectionDelay>${skynet.logging.socket.appender.reconnectionDelay:-5000}</ReconnectionDelay>
         <IncludeCallerData>true</IncludeCallerData>
        <eventDelayLimit>${skynet.logging.socket.appender.event.delay.limit:-3}</eventDelayLimit>
        <queueSize>${skynet.logging.socket.appender.queue.size:-256}</queueSize>
    </appender>
```

#### 3.2.4 日志状态查看

Skynet启动后，通过访问 actuator endpoint，即可查看日志信息 

**端点名称**：Skynet-logging
**地址**：http://{agent-ip:port}/actuator/Skynet-logging，如http://*************:6230/actuator/skynet-logging

也可以在界面上直接查看agent logging状态，点击左侧的“节点管理”，在节点列表上点击想要查看日志状态的节点

![查看日志2](./img/查看日志状态1.png)

进入到节点信息页面之后，在概览页面，通过点击 *skynet-logging* 端点，直接查看日志信息

![查看日志2](./img/查看日志2.png)

下面是日志信息示例

```json
{
  "logstashProperties": {
    "enabled": false,
    "host": "127.0.0.1:19601"
  },
  "socketAppenderServer": {
    "closed": false
  },
  "socketProperties": {
    "enabled": true,
    "host": "127.0.0.1",
    "port": 4560
  }
}
```

### 3.3 manager日志配置

- **本身日志**

默认日志路径为 *{Skynet安装路径}/log/<EMAIL>*。日志配置同agent，如果使用了logstash也会采集到了ES。

- **部署日志**

日志文件规则为 *agent_deploy_${IP}.log*，如：agent_deploy_172.31.164.60.log

- **审计日志**

记录登录，增加、修改、删除、记录的日志。日志路径为 {Skynet安装路径}/log/<EMAIL>，审计日志的日志格式配置如下：


```properties
rolling.file.name.pattern=${LOG_PATH}/ant-xmanager@audit.%d{yyyy-MM-dd}.%i.gz}
```

 

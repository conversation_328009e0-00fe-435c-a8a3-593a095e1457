<h1>如何修改Logback日志配置</h1>

对于SpringBoot类型服务，skynet会自动注入一些属性的默认值，使其使用${SKYNET_HOME}/conf/skynet-logback.xml作为logback的配置文件。该配置文件中的配置对于日志采集至关重要。

如下面的例子

```
logging.config=file:/iflytek/server/skynet/conf/skynet-logback.xml
logging.file=/data/iflytek/server/skynet/log/<EMAIL>
logging.level.skynet.boot.demo=INFO
logging.pattern.console=%d{MM-dd HH:mm:ss.SSS} %clr(%5p) %clr([%9t]){faint} %clr(%-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}]){cyan}%clr(:){faint} %m%n
logging.pattern.file=%d{MM-dd HH:mm:ss.SSS} -%5p 11245[%t] %-40.40logger{39}[%3line{3}][%X{SKYNET_CURRENT_TRACE_ID}]: %m%n
```

如果希望服务使用自己的logback配置文件，可以在 `'服务定义'`->`'属性配置'`中，添加`'logging.config'`配置，覆盖skynet提供的默认值。
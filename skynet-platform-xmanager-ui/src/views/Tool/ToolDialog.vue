<template>
  <el-dialog
    class="ele-mod"
    :visible.sync="dialogVisible"
    width="1000px"
    :close-on-click-modal="false"
    :title="$t('157')"
    :destroy-on-close="true"
    :modal="modal"
  >
    <tool class="tool-dialog"></tool>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

import Tool from './Tool'
export default {
  name: 'ToolDialog',
  components: {
    Tool
  },
  props: {
    modal: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false
    }
  },
  created() {},
  methods: {
    open() {
      this.dialogVisible = true
    }
  }
}
</script>
<style lang="scss">
.tool-dialog.tool-container {
  padding: 0 !important;
  .el-tabs__content {
    max-height: 70vh;
  }
}
</style>

<template>
  <div id="agent-manage" class="agent-manage-contanier height-inherit" v-loading="loading" :element-loading-text="$t('598')">
    <div class="top">
      <div class="top-sort">
        <el-checkbox v-model="sorting">{{ $t('209') }}</el-checkbox>
        <el-button v-show="sorting" size="mini" icon="el-icon-check" @click="onSortConfirm" type="primary">{{ $t('75') }}</el-button>
        <prompt v-show="sorting" class="prompt-wrap">{{ $t('225') }}</prompt>
      </div>
      <div v-show="!sorting" class="top-filter">
        <div class="item">
          <span class="title">{{ $t('599') }}</span>
          <el-select v-model="searchModel.selectedStatus" :placeholder="$t('600')" size="mini" clearable class="top-filter-item">
            <el-option v-for="item in statusOpts" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
        <div class="item">
          <span class="title">{{ $t('557') }}</span>
          <el-select v-model="searchModel.selectedTag" clearable collapse-tags :placeholder="$t('125')" size="mini" class="top-filter-item">
            <el-option v-for="item in labelOpts" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </div>
        <div class="item">
          <span class="title">{{ $t('601') }}</span>
          <el-input
            :placeholder="$t('602')"
            v-model="searchInput"
            size="mini"
            style="width: 190px"
            clearable
            @keyup.enter.native="searchModel.searchInput = searchInput"
            @clear="searchModel.searchInput = ''"
          >
            <el-button slot="append" icon="el-icon-search" style="cursor: pointer" @click="searchModel.searchInput = searchInput"></el-button>
          </el-input>
        </div>
        <div class="item">
          <el-button @click="resetSearchForm">{{ $t('120') }}</el-button>
        </div>
        <div class="clear"></div>
      </div>
      <!-- 低分辨率下top-buttons可能在第二行，所以只能向左浮动，为了让它尽可向右对齐，需要一个动态填充的空div-->
      <div class="top-fullfill"></div>
      <div v-show="!sorting" class="top-buttons">
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="queryList()">{{ $t('147') }}</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-s-comment" @click="showManagerLog()">{{ $t('441') }}</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-plus" @click="onAddAgent">{{ $t('603') }}</el-button>
        <el-button
          size="mini"
          type="primary"
          plain
          icon="el-icon-caret-right"
          :disabled="!tableSelection || tableSelection.length === 0"
          @click="startAgent()"
          >{{ $t('604') }}</el-button
        >
        <el-button
          size="mini"
          type="danger"
          plain
          icon="el-icon-switch-button"
          :disabled="!tableSelection || tableSelection.length === 0"
          @click="stopAgent()"
          >{{ $t('489') }}</el-button
        >
        <el-button
          size="mini"
          type="danger"
          plain
          icon="el-icon-delete"
          :disabled="!tableSelection || tableSelection.length === 0"
          @click="deleteAgent()"
          >{{ $t('65') }}</el-button
        >
        <el-button
          size="mini"
          type="primary"
          plain
          icon="el-icon-download"
          :disabled="!tableSelection || tableSelection.length === 0"
          @click="installAgent()"
          >{{ $t('605') }}</el-button
        >
      </div>
      <div class="clear"></div>
    </div>
    <div class="main">
      <el-table
        ref="table"
        id="agent-table"
        :data="tableData"
        row-key="ip"
        header-row-class-name="headbg"
        :row-style="rowStyle"
        height="100%"
        @select="onTableRowSelect"
        @select-all="onTableRowSelect"
      >
        <el-table-column align="center" type="selection" width="40"></el-table-column>
        <el-table-column align="center" type="index" :label="$t('228')" width="50"> </el-table-column>
        <el-table-column align="left" prop="ip" label="IP" width="160">
          <template slot-scope="scope">
            <div style="display: flex; align-items: center">
              <i :class="'iconfont icon-' + scope.row.agentType" :style="linkStyle(scope.row)"></i>&nbsp;
              <el-link :style="linkStyle(scope.row)" @click="onView(scope.row)" :title="scope.row.description">{{ scope.row.ip }}</el-link>
              <el-link
                v-if="webshellEnabled && scope.row.agentType === 'server'"
                @click="openTerminal(scope.row)"
                :title="$t('606')"
                style="margin: 0 6px"
              >
                <i class="iconfont icon-terminal-fill"></i>
              </el-link>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="__view_hostname" :label="$t('607')" min-width="150" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column align="center" prop="status" :label="$t('477')" width="80">
          <template slot-scope="scope">
            <i class="status-point" :status="scope.row.status == 'ONLINE' ? 'enable' : 'disable'" />
            <span>{{ scope.row.__view_status_chs }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" prop="__view_hardware" :label="$t('608')" min-width="150" :show-overflow-tooltip="true"></el-table-column>
        <el-table-column align="left" prop="__view_os" :label="$t('609')" min-width="150" :show-overflow-tooltip="true"> </el-table-column>
        <el-table-column align="center" :label="$t('234')" min-width="150" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <el-tag v-for="tag in scope.row.serverTags" :key="tag">{{ tag }}</el-tag>
          </template>
        </el-table-column>
        <!-- <el-table-column align="center" prop="description" label="备注" min-width="150" :show-overflow-tooltip="true"> </el-table-column> -->
        <el-table-column align="center" :label="$t('610')" min-width="120" :show-overflow-tooltip="true">
          <template slot-scope="scope">
            <i
              v-show="
                skynetInfo.app_version &&
                  (scope.row.version.projectVersion !== skynetInfo.app_version['project-version'] ||
                    scope.row.version.buildSid !== skynetInfo.app_version['build-sid'])
              "
              class="el-icon-warning"
              style="margin-right: 4px; color: #e6a23c"
            ></i>
            <span>{{ scope.row.__view_version }}</span>
          </template>
        </el-table-column>
        <el-table-column :label="$t('239')" width="100" align="center" fixed="right">
          <template slot-scope="scope">
            <el-dropdown
              split-button
              plain
              size="mini"
              icon="el-icon-view"
              @command="handleCommand($event, scope.row)"
              @click="dropDownButton(scope.row)['func'](scope.row)"
            >
              {{ dropDownButton(scope.row).text }}
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item icon="el-icon-document-copy" command="edit">{{ $t('64') }}</el-dropdown-item>
                <el-dropdown-item icon="el-icon-delete" command="delete">{{ $t('65') }}</el-dropdown-item>
                <el-dropdown-item icon="el-icon-download" command="update">{{ $t('605') }}</el-dropdown-item>
                <el-dropdown-item v-if="webshellEnabled && scope.row.agentType === 'server'" icon="iconfont icon-terminal-fill" command="terminal">{{
                  $t('611')
                }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <agent-edit-dlg ref="agentEditDlg" @queryList="queryList"></agent-edit-dlg>
      <install-confirm ref="installConfirmRef"></install-confirm>
      <stop-confirm ref="stopConfirmRef"></stop-confirm>
      <confirm-dialog ref="confirmDialogRef"></confirm-dialog>
    </div>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import AgentEditDlg from './AgentEditDialog'
import DotDropdown from '@/components/common/DotDropdown'
import InstallConfirm from './InstallConfirm'
import StopConfirm from './StopConfirm'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
// import Sort from '@/components/Sort/Sort'
import { newSortableTable } from '@/common/util'
import { mapGetters } from 'vuex'
import Prompt from '@/components/common/Prompt'
export default {
  name: 'AgentManage',
  components: {
    AgentEditDlg,
    DotDropdown,
    InstallConfirm,
    StopConfirm,
    ConfirmDialog,
    Prompt
  },
  data() {
    return {
      statusOpts: [
        {
          value: 'ONLINE',
          label: locale.t('9')
        },
        {
          value: 'OFFLINE',
          label: locale.t('10')
        },
        {
          value: 'BOOTING',
          label: locale.t('11')
        }
      ],
      dropMenuEvents: [
        {
          label: locale.t('605'),
          funcName: 'installAgent'
        }
      ],
      searchModel: {
        selectedStatus: '',
        selectedTag: '',
        searchInput: ''
      },
      searchInput: '',
      labelOpts: [],
      cursorSelectMode: false,
      isCreate: true,
      list: [],
      loading: false,
      tableSelection: [],
      sorting: false,
      sortingBackup: [],
      sortable: null
    }
  },
  mounted() {
    this.queryList()
    this.sortable = newSortableTable('agent-table', this, 'list')
    this.$api.tag.getServerTags().then(d => {
      if (d === null || d.length === 0) {
        return
      }
      this.labelOpts = d.map(tag => {
        return {
          value: tag,
          label: tag
        }
      })
    })
  },
  watch: {
    sorting(val) {
      if (!this.sortable) {
        return
      }
      if (val) {
        this.sortable.option('disabled', false)
        this.sortingBackup = Array.from(this.list)
      } else {
        this.sortable.option('disabled', true)
        /**
         * 解决表格数据还原后不重新渲染问题
         */
        this.$nextTick(() => {
          this.list = this.sortingBackup
        })
      }
    }
  },
  computed: {
    ...mapGetters(['skynetInfo', 'uiVars']),
    webshellEnabled() {
      return this.uiVars.webshell_enabled
    },
    tableData() {
      return this.list.filter(item => {
        let statusSatisfied = !this.searchModel.selectedStatus || item.status === this.searchModel.selectedStatus
        let serverTagSatisfied = !this.searchModel.selectedTag || (item.serverTags && item.serverTags.indexOf(this.searchModel.selectedTag) > -1)
        let searchSatisfied = !this.searchModel.searchInput || (item.ip && item.ip.indexOf(this.searchModel.searchInput) > -1)
        return statusSatisfied && serverTagSatisfied && searchSatisfied
      })
    }
  },
  methods: {
    onView(agent) {
      const routerName = agent.agentType === 'kubernetes' ? 'kubernetesMain' : 'AgentDetailMain'
      this.$router.push({
        name: routerName,
        params: {
          ip: agent.ip,
          agent,
          module: 'defalut'
        }
      })
    },
    resetSearchForm() {
      this.searchInput = ''
      for (let k in this.searchModel) {
        this.searchModel[k] = ''
      }
    },
    dropDownButton(row) {
      return row.status === 'ONLINE'
        ? {
          text: locale.t('489'),
          func: this.stopAgent
        }
        : {
          text: locale.t('604'),
          func: this.startAgent
        }
    },
    handleCommand(cmd, agent) {
      if (cmd === 'edit') {
        this.editAgent(agent)
      } else if (cmd === 'delete') {
        this.deleteAgent(agent)
      } else if (cmd === 'update') {
        this.installAgent(agent)
      } else if (cmd === 'terminal') {
        this.openTerminal(agent)
      }
    },
    editAgent(agent) {
      this.$refs.agentEditDlg.open(false, agent)
    },
    onTableRowSelect(selection, row) {
      this.tableSelection = selection
    },
    onAddAgent() {
      this.$refs.agentEditDlg.open(true, null)
    },
    startAgent(agent) {
      let agents = agent ? [agent] : this.$refs.table.selection
      this.doAgentOperation(agents, 'start')
    },
    stopAgent(agent) {
      let agents = agent ? [agent] : this.$refs.table.selection
      this.doAgentOperation(agents, 'stop')
    },
    deleteAgent(agent) {
      let agents = agent ? [agent] : this.$refs.table.selection
      let onlineCount = agents.filter(agent => agent.status === 'ONLINE').length
      if (onlineCount > 0) {
        this.$message.info(locale.t('612'))
        return
      }
      this.doAgentOperation(agents, 'delete')
    },
    installAgent(agent) {
      let agents = agent ? [agent] : this.$refs.table.selection
      let nonOfflineCount = agents.filter(agent => agent.status !== 'OFFLINE').length
      if (nonOfflineCount > 0) {
        this.$message.info(locale.t('613'))
        return
      }
      this.doAgentOperation(agents, 'install')
    },
    openTerminal(agent) {
      window.sessionStorage.setItem('x-current-agent', JSON.stringify(agent))
      window.open(`/#/terminal?ip=${agent.ip}`)
    },
    doAgentOperation(agents, operation) {
      if (!agents || agents.length === 0) {
        this.$message.info(locale.t('614'))
        return
      }
      const operationMap = {
        start: {
          operationChs: locale.t('604'),
          filter: row => row.status === 'OFFLINE',
          api: this.$api.agent.startAgent
        },
        stop: {
          operationChs: locale.t('489'),
          filter: row => row.status === 'ONLINE',
          api: this.$api.agent.stopAgent
        },
        delete: {
          operationChs: locale.t('65'),
          filter: null,
          api: this.$api.agent.deleteAgent
        },
        install: {
          operationChs: locale.t('615'),
          filter: null,
          api: this.$api.agent.install
        }
      }
      if (!(operation in operationMap)) {
        console.error('invalid agent operation', operation)
        return
      }
      const __this = this
      let { operationChs, filter, api } = operationMap[operation]
      let filteredAgents = filter ? agents.filter(filter) : agents
      let ipList = filteredAgents.map(agent => agent.ip)
      let confirmCallback = confirmCallbackPara => {
        let promises = ipList.map(ip => {
          if (operation === 'install') {
            let { isInstallDocker, isForce } = confirmCallbackPara
            return api(ip, isForce, isInstallDocker)
          } else if (operation === 'stop') {
            let { stopActions } = confirmCallbackPara
            return api(ip, stopActions)
          }
          return api(ip)
        })
        Promise.allSettled(promises).then(results => {
          let failedCount = results.filter(result => {
            return result.status === 'rejected'
          }).length
          let successCount = results.length - failedCount
          if (failedCount > 0) {
            let message =
              results.length === 1
                ? `${locale.t('14')}${operationChs}${locale.t('616')}`
                : `${locale.t('617')}${operationChs}${successCount}${locale.t('618')}${failedCount}${locale.t('619')}${operationChs}${locale.t(
                  '616'
                )}`
            __this.$message({
              message,
              type: 'error'
            })
          } else {
            __this.$message({
              message: `${locale.t('14')}${operationChs}${locale.t('617')}`,
              type: 'success'
            })
          }
          __this.queryList()
        })
      }
      if (operation === 'install') {
        this.$refs.installConfirmRef.open(ipList, confirmCallback)
      } else if (operation === 'stop') {
        this.$refs.stopConfirmRef.open(ipList, confirmCallback)
      } else {
        let title = `${locale.t('620')}${operationChs}${locale.t('621')}`
        this.$refs.confirmDialogRef.open(title, ipList, confirmCallback)
      }
    },
    // 刷新列表
    queryList() {
      // todo 重置筛选条件
      const __this = this
      this.loading = true
      this.$api.agent
        .getAgents()
        .then(d => {
          __this.list = d
        })
        .finally(() => {
          __this.loading = false
        })
    },
    showManagerLog() {
      this.$router.push('/log/manager')
    },
    rowStyle({ row }) {
      let ret = {}
      if (row.status !== 'ONLINE') {
        Object.assign(ret, {
          color: '#C0C4CC'
        })
      }
      if (this.sorting) {
        Object.assign(ret, {
          cursor: 'move'
        })
      }
      return ret
    },
    linkStyle(row) {
      return row.status === 'ONLINE'
        ? {
          color: '#3b74f0'
        }
        : {
          color: '#C0C4CC'
        }
    },
    onSortConfirm() {
      let ipList = this.list.map(v => v.ip)
      this.$api.view.updateAgentOrder(ipList).then(() => {
        this.sortingBackup = this.list
        this.sorting = false
        // this.queryList()
      })
    }
  }
}
</script>
<style lang="scss">
@import '@/styles/variables.scss';

.agent-manage-contanier {
  background: #fff;
  padding: 15px;
  overflow: auto;

  .el-select {
    width: 150px !important;
  }

  .el-tag {
    height: 25px;
    line-height: 25px;
    padding: 0 5px;
  }

  .el-tag + .el-tag {
    margin-left: 5px;
  }

  .opreate-dialog-content {
    display: inline-block;
    width: 100%;
    text-align: center;
    margin-top: 50px;
    margin-bottom: 50px;
  }

  .top {
    width: 100%;

    // overflow: hidden;
    // height: 45px;
    .top-sort,
    .top-filter,
    .top-buttons,
    .top-fullfill {
      float: left;
      height: 28px;
      margin-bottom: 15px;
    }

    .top-sort {
      min-width: 100px;

      .el-checkbox {
        margin-top: 5px;
        margin-right: 20px;
      }
    }

    .top-filter {
      width: 728px; //用F12元素查看器得到的实际宽度

      // height: 29px;
      .item {
        float: left;
        height: 28px;
        line-height: 28px;

        .title {
          margin-right: 10px;
        }
      }

      .item + .item {
        margin-left: 10px;
      }
    }

    .top-fullfill {
      width: calc(100% - 100px - 728px - 700px);
    }

    .top-buttons {
      width: 700px; //用F12元素查看器得到的实际宽度

      // height: 29px;
      .el-button + .el-button {
        margin-left: 5px;
      }
    }
  }

  .main {
    height: calc(100% - 43px);

    .inline-button {
      padding: 0;
    }

    .inline-drop {
      .el-icon-more:before {
        font-size: 14px;
      }
    }
  }
}
</style>
<style lang="scss">
#agent-manage .el-tag {
  height: 25px;
  line-height: 25px;
  padding: 0 5px;
}

#agent-manage .el-tag + .el-tag {
  margin-left: 5px;
}

#opreate-dialog-content {
  display: inline-block;
  width: 100%;
  text-align: center;
  margin-top: 50px;
  margin-bottom: 50px;
}

.prompt-wrap {
  display: inline-block;
  margin-left: 20px;
}

.icon-server,
.icon-kubernetes {
  color: #367ae0;
}
</style>

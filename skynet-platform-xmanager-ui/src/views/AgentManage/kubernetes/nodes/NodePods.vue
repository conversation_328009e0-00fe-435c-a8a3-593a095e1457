<template>
  <div>
    <el-table
      :data="tableData"
      header-row-class-name="headbg"
      :height="_setTHeight(120)"
      class="pod-list"
      v-loading="loading"
      :row-class-name="deletionRowClassName"
      :element-loading-text="$t('628')"
    >
      <el-table-column type="index" width="50" align="center" :label="$t('228')" />
      <el-table-column :label="$t('640')" prop="metadata.name" min-width="120" sortable show-overflow-tooltip>
        <template slot-scope="scope">
          <el-link type="primary" @click="onPodDetail(scope.row)">
            {{ scope.row.metadata.name }}
          </el-link>
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('641')" width="120" prop="metadata.namespace" sortable show-overflow-tooltip />
      <el-table-column :label="$t('477')" width="160" prop="status.phase" sortable show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-for="(item, index) in scope.row.status.conditions" :key="index">
            <el-popover placement="top-start" trigger="hover">
              <div class="status-process">
                <div>
                  <i :class="['iconfont', item.ok ? (item.status ? ' icon-success-fill' : 'icon-success-fill offline') : 'icon-reeor-fill']"></i>
                  <span> {{ item.name }} </span> <span>({{ item.lastTransitionAt }})</span>
                </div>
                <div v-if="!item.status">{{ $t('845') }}{{ item.message }}</div>
                <div v-if="!item.status">{{ $t('846') }}{{ item.reason }}</div>
              </div>
              <span slot="reference" style="cursor: pointer">
                <i :class="['iconfont', item.ok ? (item.status ? ' icon-success-fill' : 'icon-success-fill offline') : 'icon-reeor-fill']"></i>
              </span>
            </el-popover>
          </span>
          <span>{{ scope.row.status.phase }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('852')" width="120" show-overflow-tooltip>
        <template slot-scope="scope">
          <p v-for="(item, index) in scope.row.status.podIPs" :key="index">{{ item.ip }}</p>
        </template>
      </el-table-column>
      <el-table-column :label="$t('947')" width="80" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-for="(item, index) in scope.row.containers" :key="index">
            <el-popover placement="top-start" trigger="hover">
              <div class="status-process">
                <div>
                  <i :class="['iconfont', item.ready ? 'icon-success-fill' : 'icon-reeor-fill', `pod-${item.state.type}`]"></i>
                  <span> {{ item.state.type }} </span>
                  <span
                    ><b>{{ item.name }}</b> [{{ item.group }}]
                  </span>
                </div>
                <div>
                  <span> {{ item.state.reason }} </span>
                </div>
                <div>
                  <span> {{ item.state.message }} </span>
                </div>
                <div v-if="item.state.startedAt">
                  <span>{{ $t('948') }}{{ item.state.startedAt }} </span>
                </div>
                <div>
                  <span>{{ $t('949') }}{{ item.image }} </span>
                </div>
                <div v-if="item.state.exitCode != null">
                  <span>{{ $t('950') }}{{ item.state.exitCode }} </span>
                </div>
              </div>
              <span slot="reference" style="cursor: pointer">
                <i :class="['iconfont', item.ready ? 'icon-success-fill' : 'icon-reeor-fill', `pod-${item.state.type}`]"></i>
              </span>
            </el-popover>
          </span>
        </template>
      </el-table-column>
      <el-table-column :label="$t('644')" width="100" prop="metadata.creationAt" align="center" sortable show-overflow-tooltip>
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.metadata.creationAt" placement="top-start">
            <span> {{ scope.row.metadata.creationAge }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column :label="$t('239')" width="150" align="center">
        <template slot-scope="scope">
          <el-link type="primary" @click="onYAML(scope.row)">YAML</el-link>
        </template>
      </el-table-column>
    </el-table>
    <yaml-dialog :ip="ip" :value="yamlContent" :title="yamlTitle" :readonly="true" v-if="yamlShowDialog" @close="yamlShowDialog = false" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import pod from '@/utils/k8s/pod'
import YamlDialog from '@/views/AgentManage/kubernetes/components/yaml-dialog/index.vue'
export default {
  props: ['ip', 'nodename'],
  components: {
    YamlDialog
  },
  data() {
    return {
      loading: false,
      tableData: [],
      yamlTitle: '',
      yamlContent: '',
      yamlShowDialog: false
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData(val) {
      this.$api.kubernetes
        .getPods(this.ip, {
          fieldSelector: `spec.nodeName=${this.nodename}`
        })
        .then(res => {
          this.tableData = pod.parsePods(res)
        })
    },
    deletionRowClassName({ row, rowIndex }) {
      return row.metadata.deletionTimestamp ? 'deletion-row' : ''
    },
    onPodDetail(row) {
      this.$router.push({
        name: 'k8sPodDetail',
        params: {
          nodeIp: this.$route.params.nodeIp,
          namespace: row.metadata.namespace,
          pod: row.metadata.name
        }
      })
    },
    onYAML(row) {
      this.loading = true
      this.$api.kubernetes
        .getPodYaml(this.ip, row.metadata.namespace, row.metadata.name)
        .then(res => {
          this.yamlTitle = `${locale.t('840')}${row.metadata.name}`
          this.yamlContent = res
          this.yamlShowDialog = true
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>
<style scoped lang="scss">
.status-process {
  p {
    width: 280px;
    display: block;
    justify-content: space-between;
  }
}
</style>

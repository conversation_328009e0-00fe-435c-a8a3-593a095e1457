<template>
  <div class="sky-detail-contanier" v-loading="loading" :element-loading-text="loadingText">
    <div class="toolbar">
      <div class="left">
        <span>{{ $t('665') }}</span>
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item :to="{ name: 'kubernetesMain', params: { module: 'nodes' } }"> {{ this.ip }} </el-breadcrumb-item>
          <el-breadcrumb-item :to="{ name: 'kubernetesMain', params: { module: 'daemonsets' } }">{{ this.namespace }} </el-breadcrumb-item>
          <el-breadcrumb-item>{{ this.name }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="right">
        <el-button size="mini" type="primary" icon="el-icon-refresh" @click="onRefresh">{{ $t('147') }}</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-document" @click="onYAML">YAML</el-button>
        <el-button size="mini" type="primary" plain icon="el-icon-monitor" @click="onTerminal">{{ $t('666') }}</el-button>

        &nbsp;&nbsp;
        <el-dropdown split-button size="mini" @command="handleCommand($event)" @click="onEditImageVersion()">
          <i class="el-icon-location-information"></i>{{ $t('667')
          }}<el-dropdown-menu slot="dropdown">
            <el-dropdown-item icon="el-icon-odometer" command="strategy">{{ $t('668') }}</el-dropdown-item>
            <el-dropdown-item icon="el-icon-refresh-right" command="restart">{{ $t('669') }}</el-dropdown-item>
            <el-dropdown-item icon="el-icon-delete" command="delete">{{ $t('670') }}</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="clear"></div>
    </div>
    <div class="body deployments-detail-content">
      <el-tabs v-model="activeTab" tab-position="top">
        <el-tab-pane :label="$t('671')" name="metadata">
          <metadata-detail :target="daemonset" />
        </el-tab-pane>
        <el-tab-pane :label="$t('672')" name="runtime">
          <set-detail :target="daemonset" :labels="labels" :name="name" :kind="'daemonset'" />
        </el-tab-pane>
      </el-tabs>
    </div>
    <yaml-dialog :ip="ip" :value="yamlContent" :title="yamlTitle" v-if="yamlShowDialog" @close="yamlShowDialog = false" @refresh="yamlRefresh" />

    <daemonset-strategy ref="strategy" :callback="onRefresh" />
    <image-setting ref="image" :callback="onRefresh" :kind="'daemonset'" />
    <pod-terminal ref="terminal" />
    <confirm-dialog ref="confirmDialogRef" :isDragVerify="true" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import MetadataDetail from './MetadataDetail'
import SetDetail from './SetDetail'
import YamlDialog from '@/views/AgentManage/kubernetes/components/yaml-dialog/index.vue'
import DaemonsetStrategy from '@/views/AgentManage/kubernetes/detail/dialog/DaemonsetStrategy.vue'
import PodTerminal from '@/views/AgentManage/kubernetes/detail/dialog/PodTerminal.vue'
import ImageSetting from '@/views/AgentManage/kubernetes/detail/dialog/ImageSetting.vue'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import EventBus from '@/components/event/EventBus'
import qs from 'querystring'
import k8sUtil from '@/utils/k8s/com'
import pod from '@/utils/k8s/pod'
export default {
  name: 'k8sDaemonsetDetail',
  components: {
    MetadataDetail,
    SetDetail,
    YamlDialog,
    DaemonsetStrategy,
    ImageSetting,
    ConfirmDialog,
    PodTerminal
  },
  data() {
    return {
      activeTab: 'runtime',
      loading: false,
      loadingText: locale.t('628'),
      daemonset: {},
      labels: {},
      yamlTitle: '',
      yamlContent: '',
      yamlShowDialog: false,
      yamlRefresh: () => {}
    }
  },
  computed: {
    //   path: ':ip/namespace/:namespace/daemonset/:daemonset',
    ip() {
      return this.$route.params.ip
    },
    namespace() {
      return this.$route.params.namespace
    },
    name() {
      return this.$route.params.daemonset
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.loadingText = locale.t('673')
      this.loading = true
      this.$api.kubernetes
        .getDaemonset(this.ip, this.namespace, this.name)
        .then(res => {
          this.daemonset = k8sUtil.parseK8sDataItem(res)
          this.labels = this.daemonset.spec.selector.matchLabels
        })
        .finally(() => {
          this.loading = false
        })
    },
    onRefresh(delay) {
      if (delay) {
        setTimeout(() => {
          this.initData()
        }, delay)
      } else {
        this.initData()
      }
    },
    onYAML() {
      this.loadingText = locale.t('674')
      this.loading = true
      this.$api.kubernetes
        .getDaemonsetYaml(this.ip, this.namespace, this.name)
        .then(res => {
          this.yamlTitle = `${locale.t('675')}${this.name}`
          this.yamlContent = res
          this.yamlShowDialog = true
          this.yamlRefresh = this.onRefresh
        })
        .finally(() => {
          this.loading = false
        })
    },
    onTerminal() {
      let target = this.daemonset
      this.loadingText = locale.t('676')
      this.loading = true
      this.$api.kubernetes
        .getPods(this.ip, {
          labelSelector: decodeURIComponent(qs.stringify(target.spec.selector.matchLabels, ',', '='))
        })
        .then(res => {
          this.$refs.terminal.open(this.ip, target, pod.parsePods(res))
        })
        .finally(() => {
          this.loading = false
        })
    },
    onEditImageVersion() {
      this.$refs.image.open(this.ip, this.daemonset)
    },
    onRestart() {
      let confirmCallback = () => {
        this.loadingText = locale.t('677')
        this.loading = true
        // TODO:
        this.$api.kubernetes
          .daemonsetRestart(this.ip, this.namespace, this.name)
          .then(res => {
            this.$message.success(`${locale.t('678')}${this.name}${locale.t('679')}`)
            this.onRefresh(2000)
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('680')}${this.name}] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    onDeleteDeploy() {
      let confirmCallback = () => {
        this.loadingText = locale.t('681')
        this.loading = true
        this.$api.kubernetes
          .deleteDaemonset(this.ip, this.namespace, this.name)
          .then(res => {
            this.$message.success(`${locale.t('678')}${this.name}${locale.t('682')}`)
            // 关闭 窗口
            EventBus.$emit('close-tag', this.$route)
          })
          .finally(() => {
            this.loading = false
          })
      }
      let title = `${locale.t('683')}${this.name}] ?`
      let names = []
      this.$refs.confirmDialogRef.open(title, names, confirmCallback)
    },
    handleCommand(cmd) {
      if (cmd === 'strategy') {
        this.$refs.strategy.open(this.ip, this.daemonset)
      } else if (cmd === 'restart') {
        this.onRestart()
      } else if (cmd === 'delete') {
        this.onDeleteDeploy()
      }
    }
  }
}
</script>
<style lang="scss">
.deployments-detail-content {
  height: calc(100% - 51px);

  .el-tabs.el-tabs--top {
    height: 100%;

    .el-tabs__content {
      height: calc(100% - 55px);

      .el-tab-pane {
        height: 100%;
      }
    }
  }
}
</style>

<template>
  <el-dialog :visible.sync="dialogVisible" width="80%" :close-on-click-modal="false" :destroy-on-close="true" :title="$t('775')">
    <div class="body">
      <div style="padding:5px">
        <span class="left">{{ $t('776') }}{{ sourceRevision }}</span>
        <span class="right">{{ $t('777') }}{{ targetRevision }}</span>
      </div>
      <code-diff
        class="code-diff"
        :old-string="sourceYmal"
        :new-string="targetYmal"
        :context="context"
        width="100%"
        outputFormat="side-by-side"
        :useHightlight="useHightlight"
        :isShowNoChange="isShowNoChange"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" icon="el-icon-check" type="primary" @click.stop="onConfirm">{{ $t('75') }}</el-button>
      <el-button size="mini" icon="el-icon-close" @click="dialogVisible = false">{{ $t('76') }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

window.jsyaml = require('js-yaml')
export default {
  name: 'ReplicasetDiff',
  props: {
    callback: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      sourceYmal: '',
      targetYmal: '',
      sourceRevision: '',
      targetRevision: '',
      useHightlight: true,
      context: 9999,
      isShowNoChange: true,
      confirmCallback: null
    }
  },
  computed: {},
  methods: {
    open(source, target, confirmCallback) {
      this.sourceYmal = this.filter(source)
      this.targetYmal = this.filter(target)
      this.sourceRevision = source.metadata.annotations['deployment.kubernetes.io/revision']
      this.targetRevision = target.metadata.annotations['deployment.kubernetes.io/revision']
      this.confirmCallback = confirmCallback
      this.dialogVisible = true
    },
    onDialogClose() {
      this.dialogVisible = false
    },
    onConfirm() {
      this.dialogVisible = false
      if (this.confirmCallback) this.confirmCallback()
    },
    filter(json) {
      let template = json.spec.template
      if (template.metadata.labels) {
        delete template.metadata.labels['pod-template-hash']
      }
      return window.jsyaml.dump(template)
    }
  }
}
</script>
<style lang="scss">
.d2h-file-side-diff {
  margin-bottom: -5px;
  max-height: 500px;
  overflow: scroll;
}

.d2h-code-side-line {
  padding: 0 0;
}

.d2h-code-side-linenumber {
  padding: 0 0.5rem;
  position: relative;
  line-height: 20px;
  height: 20px;
}
</style>

<template>
  <div>
    <el-table :data="items" header-row-class-name="headbg" v-loading="loading" :element-loading-text="$t('598')">
      <el-table-column align="center" type="index" :label="$t('228')" width="50"></el-table-column>
      <el-table-column align="left" :label="$t('625')" min-width="130" prop="name" show-overflow-tooltip>
        <template slot-scope="scope">
          <span> {{ scope.row.name }} ({{ scope.row.ip }}) </span>
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('626')" min-width="150">
        <template slot-scope="scope">
          <div class="status-cell">
            <el-progress :text-inside="true" :stroke-width="16" :percentage="scope.row.cpuPercentage"></el-progress>
            <span> {{ formatCpuCores(scope.row.cpuUsed) }} </span> /<span> {{ scope.row.cpuTotal }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" :label="$t('627')" min-width="150">
        <template slot-scope="scope">
          <div class="status-cell">
            <el-progress :text-inside="true" :stroke-width="16" :percentage="scope.row.memPercentage"></el-progress>
            <span> {{ formatMemoryBytes(scope.row.memUsed) }}</span> /<span> {{ formatMemoryBytes(scope.row.memTotal) }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import { convertCpuCores, convertMemoryBytes } from '@/utils/quantity'
export default {
  props: ['ip'],
  data() {
    return {
      items: [],
      cluster: {
        name: `${locale.t('474')}`,
        ip: this.ip,
        cpuUsed: 0.0,
        cpuTotal: 0,
        cpuPercentage: 0,
        memUsed: 0.0,
        memTotal: 0,
        memPercentage: 0
      },
      loading: false
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      this.loading = true
      this.$api.kubernetes
        .getNodes(this.ip)
        .then(res => {
          this.items = []
          this.items.push(this.cluster)
          let nodes = res
          nodes.forEach(x => {
            let item = {
              cpuUsed: 0,
              memUsed: 0
            }
            item.name = x.metadata.name
            item.ip = this.dowithRow(x.status.addresses, 'type', 'InternalIP', 'address')
            item.cpuTotal = parseInt(x.status.capacity.cpu)
            item.memTotal = parseInt(x.status.capacity.memory.replace(/Ki/, '')) * 1024
            this.items.push(item)
            this.cluster.cpuTotal += item.cpuTotal
            this.cluster.memTotal += item.memTotal
          })
          this.cluster.total = nodes.length
          this.showMetrics(this.items)
        })
        .finally(() => {
          this.loading = false
        })
    },
    showMetrics(items) {
      this.$api.kubernetes.getNodeMetrics(this.ip, {}).then(res => {
        res.forEach(x => {
          let nodes = items.filter(y => {
            return y.name === x.metadata.name
          })
          if (nodes.length === 1) {
            nodes[0].cpuUsed = convertCpuCores(x.usage.cpu)
            nodes[0].memUsed = convertMemoryBytes(x.usage.memory)
            this.cluster.cpuUsed += nodes[0].cpuUsed
            this.cluster.memUsed += nodes[0].memUsed
          }
        })
        items.forEach(x => {
          x.cpuPercentage = this.getPercentage(x.cpuUsed, x.cpuTotal)
          x.memPercentage = this.getPercentage(x.memUsed, x.memTotal)
        })
      })
    },
    formatCpuCores(cpu) {
      return cpu.toFixed(2)
    },
    formatMemoryBytes(memory) {
      return (memory / 1024 / 1024 / 1024).toFixed(2) + 'GB'
    },
    getPercentage(current, total) {
      return Number.parseFloat(((current / total) * 100).toFixed(2))
    },
    dowithRow(list, prop, value, key) {
      const findObj = list.find(item => item[prop] === value)
      return findObj ? findObj[key] : ''
    }
  }
}
</script>
<style scoped lang="scss">
.status-cell {
  padding: 0 10px;
}
</style>

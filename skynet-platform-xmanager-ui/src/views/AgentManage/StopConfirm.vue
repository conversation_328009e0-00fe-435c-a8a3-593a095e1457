<template>
  <confirm-dialog ref="confirmDialogRef" :isDragVerify="false">
    <div slot="append" class="append">
      <div>
        <el-checkbox v-model="stopActions">{{ $t('1014') }}</el-checkbox>
      </div>
    </div>
  </confirm-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
export default {
  name: 'InstallConfirm',
  components: {
    ConfirmDialog
  },
  data() {
    return {
      stopActions: false
    }
  },
  methods: {
    open(operateNames, confirmCallback) {
      this.stopActions = false
      const thisVue = this
      let cb = () => {
        confirmCallback({
          stopActions: thisVue.stopActions
        })
      }
      this.$refs.confirmDialogRef.open(locale.t('1015'), operateNames, cb)
    }
  }
}
</script>
<style scoped lang="scss">
.append {
  margin-top: 20px;
  & > div {
    margin-top: 8px;
  }
}
</style>

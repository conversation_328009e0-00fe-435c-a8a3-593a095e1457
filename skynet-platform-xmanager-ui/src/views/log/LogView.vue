<template>
  <div class="logView-contanier">
    <!-- 头部 -->
    <div class="logview-header">
      <div class="left">
        <span>{{ paramData.title }}</span>
      </div>
      <div class="right">
        <el-button size="mini" type="primary" plain @click="refresh()">{{ $t('147') }}</el-button>
        <el-button size="mini" type="primary" @click="track()">{{ trackBtnText }}</el-button>
        <el-button size="mini" type="primary" plain @click="cleanLog()">{{ $t('1052') }}</el-button>
        <el-button size="mini" type="danger" plain v-if="paramData && paramData.deleteUri" @click="del()">{{ $t('65') }}</el-button>
        <el-button size="mini" type="primary" plain v-if="paramData && paramData.downloadUri" @click="download()">{{ $t('1053') }}</el-button>
      </div>
    </div>

    <!-- 日志内容 -->
    <div class="log-box">
      <div class="log-body">
        <div v-for="(log, index) in logs" :key="index">
          <div class="log-item">
            <span class="log-index">{{ log.index }}.</span>
            <span v-if="!paramData.isEvent" class="log-text" v-html="log.text"></span>
            <span v-if="paramData.isEvent" class="log-text" :class="[log.text.action]"
              >[{{ log.text.timestamp }}] {{ log.text.address }} [{{ log.text.app }}] {{ log.text.desc }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志底部 -->
    <div class="sky-float-box">
      <el-button size="mini" :title="$t('1054')" icon="el-icon-top" class="top" circle @click="scrollToTop()"></el-button>
      <el-button size="mini" :title="$t('1055')" icon="el-icon-bottom" class="bottom" circle @click="scrollToButtom()"> </el-button>
    </div>
    <!-- 用于切换焦点 -->
    <el-input ref="input" style="opacity:0;display:none"></el-input>
    <confirm-dialog ref="confirmDialog"></confirm-dialog>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import request from '@/axios/request'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
export default {
  components: {
    ConfirmDialog
  },
  props: {
    /**
      {
        // 标题
        title: '',
        // 获取日志地址
        logUri: '',
        // 删除日志地址
        deleteUri: '',
        // 下载日志地址
        downloadUri: '',
        // 事件输出，额外处理
        isEvent: false
      }
     */
    param: {
      type: Object,
      required: false
    }
  },
  data() {
    return {
      // 避免出现父组件直接传null值的属性问题，通过watch param来决定何时的初始化时机
      paramData: {
        // 标题
        title: '',
        // 获取日志地址
        logUri: '',
        // 删除日志地址
        deleteUri: '',
        // 下载日志地址
        downloadUri: '',
        // 事件输出，额外处理
        isEvent: false,
        index: 0
      },
      index: 0,
      logs: [],
      socket: '',
      isPause: false,
      trackBtnText: locale.t('1056')
    }
  },
  created() {
    if (this.param && this.param.logUri) {
      this.paramData = this.param
      this.wsInit()
    }
  },
  mounted() {
    this.enterKeyup()
    this.scrollToButtom()
  },
  watch: {
    param(val) {
      if ((!this.paramData || !this.paramData.logUri) && val && val.logUri) {
        this.paramData = val
        this.wsInit()
      }
    }
  },
  methods: {
    // 清空日志
    cleanLog: function() {
      this.$refs.input.focus()
      this.logs = []
      this.index = 1
    },
    // 删除日志
    del: function() {
      if (!this.paramData || !this.paramData.deleteUri) {
        return
      }
      const that = this
      let confirmCallback = () => {
        that.$refs.input.focus()
        request
          .post(this.paramData.deleteUri, '', {
            __use_raw_response_data: true
          })
          .then(function(response) {
            that.index = 0
            that.logs = [
              {
                index: that.index,
                text: locale.t('1057')
              }
            ]
          })
          .catch(function(error) {
            console.error(error)
            that.$message.error(locale.t('1058'))
          })
      }
      this.$refs.confirmDialog.open(locale.t('1059'), [], confirmCallback)
    },
    // 下载日志
    download: function() {
      if (!this.paramData || !this.paramData.downloadUri) {
        return
      }
      this.$refs.input.focus()
      window.open(this.paramData.downloadUri)
    },
    // 暂停/继续 跟踪
    track: function() {
      this.$refs.input.focus()
      this.socket.send(this.isPause ? 'continue' : 'pause')
      this.trackBtnText = this.isPause ? locale.t('1056') : locale.t('1060')
      if (this.isPause) {
        this.scrollToButtom()
      }
      this.isPause = !this.isPause
    },
    // 页面滚动到顶部
    scrollToTop: function() {
      var div = document.getElementsByClassName('log-box')[this.paramData.index ? this.paramData.index : 0]
      if (div) {
        div.scrollTop = 0
      }
    },
    // 页面滚动到底部
    scrollToButtom: function() {
      var div = document.getElementsByClassName('log-box')[this.paramData.index ? this.paramData.index : 0]
      if (div) {
        div.scrollTop = div.scrollHeight
      }
    },
    // 回车调转底部，插入空行
    enterKey(event) {
      const code = event.keyCode ? event.keyCode : event.which ? event.which : event.charCode
      if (code === 13) {
        this.logs.push({
          index: this.index++,
          text: null
        })
        this.scrollToButtom()
      }
    },
    enterKeyupDestroyed() {
      document.removeEventListener('keyup', this.enterKey)
    },
    enterKeyup() {
      document.addEventListener('keyup', this.enterKey)
    },
    inserBlank: function() {},
    // webSocket请求
    wsInit: function() {
      if (typeof WebSocket === 'undefined') {
        alert(locale.t('1061'))
      } else {
        // console.log('try to establish websocket : %s', this.paramData.logUri)
        this.socket = new WebSocket(this.paramData.logUri)
        this.socket.onopen = this.wsOpen
        this.socket.onerror = this.wsError
        this.socket.onmessage = this.wsMessage
        this.socket.onclose = this.wsClose
      }
    },
    wsOpen: function() {
      // console.log(`websocket ${this.paramData.logUri} 连接成功`)
    },
    wsError: function(err) {
      console.error(`websocket ${this.paramData.logUri}${locale.t('1062')}`, err)
      this.socket.close()
    },
    wsMessage: function(msg) {
      if (this.logs.length > 400) {
        this.logs.shift()
      }
      if (this.paramData.isEvent) {
        this.logs.push({
          index: this.index++,
          text: JSON.parse(msg.data)
        })
      } else {
        var text = msg.data.replace('<', '&lt;').replace('>', '&gt;')
        text = text.replace('DEBUG', '<debug>DEBUG</debug>')
        text = text.replace('INFO', '<info>INFO</info>')
        text = text.replace('WARN', '<warn>WARN</warn>')
        text = text.replace('ERROR', '<error>ERROR</error>')
        text = text.replace('TRACE', '<debug>TRACE</debug>')
        this.logs.push({
          index: this.index++,
          text: text
        })
      }
      this.scrollToButtom()
    },
    wsClose: function() {
      // console.log(`websocket ${this.paramData.logUri} 连接关闭`)
    },
    refresh() {
      this.socket.close()
      this.cleanLog()
      this.wsInit()
    }
  },
  destroyed() {
    if (this.socket) {
      this.socket.close()
    }
    this.enterKeyupDestroyed()
  }
}
</script>
<style lang="scss">
.logView-contanier {
  height: 100%;

  .logview-header {
    height: 28px;

    .left {
      float: left;
      height: 28px;
      line-height: 28px;
    }

    .right {
      float: right;
      height: 28px;
    }
  }

  .log-box {
    height: calc(100% - 38px);
    overflow-x: hidden;
    overflow-y: auto;
    margin-top: 10px;
    background-color: #282828;
  }

  .log-body {
    padding: 10px;
    font-size: 13px;
    font-family: Menlo;
  }

  .log-index {
    padding-right: 5px;
    text-align: right;
    display: inline-block;
    font-weight: bold;
  }

  .log-text {
    padding-left: 5px;
  }

  .log-item {
    line-height: 1.6em;
    border-bottom: 0px solid #e7e7e7;
    color: #e7e7e7;
    word-wrap: break-word;
  }

  .log-item:hover {
    background-color: #fff;
    color: #000;
  }

  .sky-float-box {
    position: fixed;
    right: 60px;
    bottom: 50px;
    width: 28px;

    .top {
      background: #3051a7;
      margin: 0 0 10px 0;
      color: #fff;
      border-color: #3051a7;
    }

    .bottom {
      margin: 0;
    }
  }

  .stopProcess {
    background-color: #940205;
    color: #e7e7e7;
  }

  .killProcess {
    background-color: #bbbb23;
    color: #e7e7e7;
  }

  .healthCheckFail {
    background-color: #256d16;
    color: #e7e7e7;
  }
}
</style>

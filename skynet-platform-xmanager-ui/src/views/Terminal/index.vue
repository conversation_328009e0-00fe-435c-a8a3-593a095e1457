<template>
  <div class="terminal-contanier">
    <div id="terminalHeader" class="terminal-header">
      <span class="terminal-ip">
        <i class="iconfont" :class="[loginSuccess ? 'icon-success-fill' : 'icon-reeor-fill']"></i>
        <span>{{ loginForm.username }}@{{ loginForm.host }}</span>
      </span>
    </div>

    <div id="terminalContanierBox" :class="{ 'animation-contanier-box': showControl }">
      <div v-if="loginSuccess" id="xterm" class="xterm"></div>
    </div>
    <div class="right-file-box" :class="{ 'animation-file-box': showControl }">
      <div class="control-show">
        <p @click="openOrCloseControl">
          <i v-show="showControl" class="el-icon-caret-right"></i>
          <i v-show="!showControl" class="el-icon-caret-left"></i>
          <span v-show="!showControl">{{ $t('1100') }}</span>
        </p>
      </div>
      <div class="control-contanier">
        <div class="header-info">
          <p class="header-left">SSH</p>
          <div class="header-right">
            <p class="host-name">{{ loginForm.host }}</p>
            <p>{{ $t('1101') }}{{ loginForm.host }}</p>
            <p>{{ $t('1102') }}{{ loginForm.username }}</p>
          </div>
        </div>

        <div class="operation">
          <el-button type="text" @click="showNewFile = true" :disabled="tableLoading">
            <i class="iconfont icon-xinjianwenjianjia"></i>
          </el-button>

          <el-button type="text" v-if="tableLoading" disabled>
            <i class="iconfont icon-shangchuanfangshi"></i>
          </el-button>
          <el-upload v-else :action="`/skynet/api/shell/sftp/upload/${ftpSessionId}?path=${currentPath}`" :on-success="fileUploadSuccess">
            <el-button type="text" style="margin: 0 10px">
              <i class="iconfont icon-shangchuanfangshi"></i>
            </el-button>
          </el-upload>

          <el-button type="text" :disabled="tableSeleList && tableSeleList.length !== 1" @click="downloadFile">
            <i class="iconfont icon-xiazai"></i>
          </el-button>
          <el-button type="text" :disabled="tableSeleList && tableSeleList.length === 0" @click="delFile">
            <i class="iconfont icon-shanchu"></i>
          </el-button>
          <el-button type="text" :disabled="tableSeleList && tableSeleList.length !== 1" @click="openEdit">
            <i class="iconfont icon-bianjishuru"></i>
          </el-button>
          <el-button type="text" @click="getFileData">
            <i class="iconfont icon-shuaxin"></i>
          </el-button>
        </div>

        <div class="file-path">
          <span class="clickCursor" @click="backPath('/')">{{ $t('1103') }}</span>
          <PathBreadcrumb :currentPath="currentPath" @updatePath="backPath" />
        </div>

        <div v-if="showNewFile" style="padding-left: 55px">
          <i class="iconfont icon-wenjianjia"></i>
          <el-input v-model="newFileName" :placeholder="$t('1104')" style="width: 140px"></el-input>
          <i class="newfileicon el-icon-check" @click="newFile"></i>
          <i class="newfileicon el-icon-close" @click="showNewFile = false"></i>
        </div>

        <el-table
          ref="fileTableRef"
          :data="fileTableData"
          :table-layout="'auto'"
          size="mini"
          @selection-change="handleSelectionChange"
          class="fileTable"
          v-loading="tableLoading"
        >
          <el-table-column type="selection" width="55" align="center"></el-table-column>
          <el-table-column prop="text" :label="$t('640')" show-overflow-tooltip>
            <template slot-scope="scope">
              <div v-if="showEditFile && tableSeleList[0] && tableSeleList[0].id === scope.row.id" class="edit-file">
                <i class="iconfont icon-wenjianjia" v-if="scope.row.fileType === $t('1105')"></i>
                <i class="iconfont icon-wenjian1" v-else></i>
                <el-input v-model="editFileName" :placeholder="$t('1104')" style="width: 170px"></el-input>
                <i class="newfileicon el-icon-check" @click="editFile"></i>
                <i class="newfileicon el-icon-close" @click="showEditFile = false"></i>
              </div>
              <div v-else @click="textClick(scope.row)" class="clickCursor">
                <i class="iconfont icon-wenjianjia" v-if="scope.row.fileType === $t('1105')"></i>
                <i class="iconfont icon-wenjian1" v-else></i>
                <span>{{ scope.row.text }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="size" :label="$t('935')" width="80" align="right" show-overflow-tooltip></el-table-column>
          <el-table-column prop="modifiedDate" :label="$t('720')" width="120" align="right" show-overflow-tooltip></el-table-column>
        </el-table>
      </div>
    </div>
    <LoginDialog :dialogVisible="dialogVisible" @successLogin="loginGetToken" />

    <confirm ref="deleteConfirmRef" :isDragVerify="true"></confirm>
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import { refreshToken } from '@/axios/api/auth'
import ui from '@/axios/api/ui'
import 'xterm/css/xterm.css'
import { Terminal } from 'xterm'
// import { AttachAddon } from 'xterm-addon-attach'
import { FitAddon } from 'xterm-addon-fit'
import ConfirmDialog from '@/components/Confirm/ConfirmDialog.vue'
import PathBreadcrumb from './PathBreadcrumb.vue'
import LoginDialog from './LoginDialog.vue'
export default {
  name: 'Terminal',
  components: {
    confirm: ConfirmDialog,
    PathBreadcrumb,
    LoginDialog
  },
  data() {
    return {
      loginForm: {
        host: '',
        username: ''
      },
      loginSuccess: false,
      dialogVisible: false,
      socket: null,
      term: null,
      fitAddon: null,
      row: 80,
      col: 40,
      wp: 0,
      hp: 0,
      fileTableData: [],
      ftpSessionId: '',
      tableSeleList: [],
      currentPath: '/',
      tableLoading: false,
      newFileName: locale.t('1106'),
      newFileLoading: false,
      showNewFile: false,
      editFileName: '',
      showEditFile: false,
      showControl: false,
      websftpEnabled: false,
      commandCount: 0
    }
  },
  async mounted() {
    let resp = await ui.getUIVariables()
    this.websftpEnabled = resp.websftp_enabled
    if (!resp.webshell_enabled) {
      this.$alert(locale.t('1099'))
      return
    }
    // this.uiVars.websftp_enabled
    // 窗口大小变化时自动调整 tty size
    this.sizeInit()
    // window.addEventListener('resize', this.sizeInit)
    // 如果 URL 中带 ip 参数，则自动填入登录框
    const searchParams = new URLSearchParams(location.hash.indexOf('?') > -1 ? location.hash.substring(location.hash.indexOf('?')) : location.search)
    // 如果 sessionStorage 中有节点登录信息，则自动登录
    try {
      const agent = JSON.parse(window.sessionStorage.getItem('x-current-agent'))
      // 首次进入使用本地存储的信息进行登录
      if (agent.ip === searchParams.get('ip')) {
        this.loginGetToken({
          host: agent.ip,
          port: agent.sshPort,
          username: agent.sshUser,
          password: agent.sshPassword
        })
      } else {
        this.dialogVisible = true
      }
    } catch (error) {
      this.dialogVisible = true
      // console.log('本地存储异常', error)
    }
  },
  methods: {
    // 登录获取token
    loginGetToken(param, callBack) {
      // 用于右侧显示主机信息
      this.loginForm.host = param.host
      this.loginForm.username = param.username
      this.$api.terminal
        .loginFtp(param)
        .then(res => {
          if (res) {
            this.ftpSessionId = res
            this.dialogVisible = false
            callBack && callBack()
            this.wsInit()
          }
        })
        .catch(() => {
          callBack && callBack()
          this.dialogVisible = true
        })
    },
    sizeInit() {
      // 根据屏幕大小动态计算 tty size
      this.wp = document.getElementById('terminalContanierBox').offsetWidth
      this.hp = document.getElementById('terminalContanierBox').offsetHeight - document.getElementById('terminalHeader').offsetHeight
      this.col = parseInt(this.wp / 9) - 6
      this.row = parseInt(this.hp / 16) - 6
      if (this.fitAddon) {
        this.fitAddon.fit()
      }
    },
    wsInit() {
      if (typeof WebSocket === 'undefined') {
        alert(locale.t('1061'))
      } else {
        let shellUri = `ws://${window.location.host}${window.location.pathname}skynet/api/shell/${this.ftpSessionId}`
        this.socket = new WebSocket(shellUri)
        this.socket.onopen = this.wsOpen
        this.socket.onerror = this.wsError
        this.socket.onmessage = this.wsMessage
        this.socket.onclose = this.wsClose
      }
    },
    wsOpen() {
      // console.log(`websocket 连接成功`)
      // this.socket.send()
      document.title = this.loginForm.host + ' - Skynet'
    },
    wsError() {
      this.socket.close()
      this.loginSuccess = false
      this.dialogVisible = true
    },
    wsMessage(msg) {
      // console.log('ws传输的数据：', msg)
      if (this.loginSuccess) {
        this.term.write(msg.data)
      } else {
        const result = msg.data
        if (result) {
          this.loginSuccess = true
          this.$nextTick(() => {
            this.initTerm()
          })
        }
      }
    },
    wsClose() {
      // console.log(`websocket 连接关闭`)
      this.loginSuccess = false
      this.dialogVisible = true
    },
    initTerm() {
      const term = new Terminal({
        rendererType: 'canvas',
        // 渲染类型
        pid: 1,
        name: 'terminal',
        rows: this.row,
        // 行数
        cols: this.col,
        // 列数
        convertEol: true,
        // 启用时，光标将设置为下一行的开头
        scrollback: 500,
        // 终端中的回滚量
        disableStdin: false,
        // 是否应禁用输入。
        cursorStyle: 'underline',
        // 光标样式
        cursorBlink: true,
        // 光标闪烁
        tabStopWidth: 8,
        // 制表宽度
        screenKeys: true,
        // Xterm下的快捷键
        theme: {
          foreground: '#FFFFFF',
          // 前景色
          background: '#000000',
          // 背景色
          cursor: 'Orange',
          // 设置光标
          lineHeight: 16
        }
      })
      // const attachAddon = new AttachAddon(this.socket)
      // term.loadAddon(attachAddon)
      this.fitAddon = new FitAddon()
      term.loadAddon(this.fitAddon)
      term.open(document.getElementById('xterm'))
      term.onData(data => {
        this.socket.send(
          JSON.stringify({
            operate: 'command',
            command: data,
            row: this.row,
            col: this.col,
            wp: this.wp,
            hp: this.hp
          })
        )
        this.commandCount = this.commandCount + 1
        // 每输入30个字符 刷新token，避免会话过期
        if (this.commandCount > 30) {
          refreshToken()
          this.commandCount = 0
        }
      })
      this.fitAddon.fit()
      term.focus()
      this.term = term
    },
    // 获取某层级下的文件树
    getFileData() {
      this.tableLoading = true
      return this.$api.terminal.getFileTree(this.ftpSessionId, this.currentPath).then(data => {
        this.tableLoading = false
        this.fileTableData = data
      })
    },
    // 点击文件夹
    textClick(row) {
      if (row.fileType === locale.t('1105')) {
        this.backPath(row.id)
      }
    },
    // 表格复选
    handleSelectionChange(selList) {
      this.tableSeleList = selList
    },
    // 点击路径返回对应层级下
    backPath(path) {
      if (this.currentPath !== path) {
        this.currentPath = path
        this.getFileData()
      }
    },
    // 下载文件
    downloadFile() {
      this.$api.terminal.downloadFile(
        this.ftpSessionId,
        // this.tableSeleList.map((item) => item.id).at(0)
        // 只下载选中的第一个文件或文件夹
        this.tableSeleList[0].id
      )
      this.$refs.fileTableRef.clearSelection()
    },
    // 删除文件
    delFile() {
      this.$confirm(locale.t('1107'), locale.t('66'), {
        confirmButtonText: locale.t('75'),
        cancelButtonText: locale.t('76'),
        type: 'warning'
      }).then(() => {
        this.$api.terminal.delFiles(this.ftpSessionId, this.tableSeleList.map(item => item.id)).then(() => {
          this.getFileData()
        })
      })
    },
    // 新建文件夹
    newFile() {
      if (!this.newFileLoading) {
        this.newFileLoading = true
        if (!this.newFileName) {
          this.$message.error(locale.t('1108'))
          return
        }
        this.$api.terminal.createDir(this.ftpSessionId, this.newFileName).then(() => {
          this.newFileLoading = false
          this.showNewFile = false
          this.getFileData()
        })
      }
    },
    openEdit() {
      this.showEditFile = true
      this.editFileName = this.tableSeleList[0].text
    },
    // 编辑文件
    editFile() {
      if (!this.editFileName) {
        this.$message.error(locale.t('1108'))
        return
      }
      // const oldPath = this.currentPath === '/' ? this.tableSeleList[0].id : this.currentPath + this.tableSeleList[0].id
      const oldPath = this.tableSeleList[0].id
      const newPath = this.currentPath === '/' ? '/' + this.editFileName : this.currentPath + '/' + this.editFileName
      this.$api.terminal.editFileName(this.ftpSessionId, oldPath, newPath).then(() => {
        this.$message.success(locale.t('1109'))
        this.showEditFile = false
        this.getFileData()
      })
    },
    fileUploadSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.getFileData()
      }
    },
    openOrCloseControl() {
      if (!this.websftpEnabled) {
        this.$alert(locale.t('1110'))
        return
      }
      this.showControl = !this.showControl
      // 右侧打开获取文件目录树
      this.showControl && this.getFileData()
      // this.$nextTick(() => {
      //   this.sizeInit()
      // })
    }
  },
  beforeDestroy() {
    if (this.socket) {
      this.socket.close()
    }
    if (this.term) {
      this.term.dispose()
    }
  }
}
</script>
<style lang="scss">
.terminal-header {
  position: fixed;
  top: 0;
  width: 100%;
  height: 36px;
  background-color: #3a3333;
  display: block;
}
.terminal-ip {
  line-height: 36px;
  text-align: center;
  padding-left: 5px;
  color: #fff;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.terminal-contanier {
  width: 100%;
  height: 100%;
  background-color: #000;
  padding-top: 36px;
}

#terminalContanierBox {
  width: 100%;
  height: 100%;
  transition: 0.5s;

  &.animation-contanier-box {
    padding-right: 500px;
  }
}

.xterm {
  height: 100%;
}

.right-file-box {
  position: fixed;
  top: 0;
  right: 0;
  width: 16px;
  height: 100%;
  background-color: #fff;
  display: flex;
  z-index: 999;
  transition: 0.5s;

  &.animation-file-box {
    width: 500px;
  }

  .control-show {
    width: 16px;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f3f3f3;

    p {
      text-align: center;
      cursor: pointer;
      i {
        font-size: 20px;
      }

      span {
        writing-mode: tb;
      }
    }
  }

  .control-contanier {
    flex: 1;
  }
}

.header-info {
  width: 100%;
  padding: 20px 20px 15px 20px;
  box-sizing: content-box;
  display: flex;
  border-bottom: 5px solid #f0f0f0;
}

.header-left {
  width: 40px;
  height: 36px;
  line-height: 36px;
  text-align: center;
  background-color: #fe7a38;
  color: #fff;
  margin-right: 20px;
  border-radius: 6px;
}

.header-right p {
  line-height: 24px;
}

.host-name {
  font-size: 16px;
  line-height: 16px;
  margin-bottom: 16px;
  color: #000;
}

.operation {
  display: flex;
  justify-content: flex-start;
  height: 38px;
  line-height: 38px;
  padding: 0px 16px;
}

.iconfont {
  font-size: 20px;
}

.file-path {
  height: 38px;
  line-height: 38px;
  background-color: #f0f0f0;
  padding-left: 16px;
  display: flex;
}

.fileTable {
  width: 100%;
  height: calc(100vh - 240px) !important;
  overflow: auto;
}

thead {
  display: none;
}

.el-table th.is-leaf .el-table td {
  border-bottom: none;
}

.el-table::before {
  height: 0;
}

.el-table .cell {
  padding: 0;
}

.icon-wenjianjia {
  color: rgb(247, 186, 42);
}

.icon-wenjian1 {
  color: rgb(192, 204, 218);
}

.clickCursor {
  cursor: pointer;
}

.newfileicon {
  color: #367ae0;
  cursor: pointer;
  margin-left: 5px;
}

.edit-file {
  z-index: 999;
  position: absolute;
  top: 4px;
}

.el-upload-list.el-upload-list--text {
  display: none;
}

.el-table .cell {
  padding: 2px 10px;
}

.icon-success-fill {
  color: green;
  font-size: 16px;
}

.icon-reeor-fill {
  color: #d81e06;
  font-size: 16px;
}
</style>

<template>
  <div class="view-by-action">
    <card
      v-for="(card, index) in cardList"
      :key="index"
      :title="card.title"
      :badgeList="card.badgeList"
      :badgeGroupList="card.badgeGroupList"
      @alloc-click="onAllocClick(card.title.event)"
    >
      <template v-slot:card__footer>
        <div class="footer">
          <footer-statis :statis="card.footer.statis" class="footer-statis" />
          <div class="clear" />
        </div>
      </template>
    </card>
    <allocate-by-action ref="allocByAction" @success="onAllocSuccess" />
  </div>
</template>
<script>
import locale from '@/i18n/index.js'

import Card from './Card'
import FooterStatis from './FooterStatis'
import { calcActionStatis } from './auxiliary'
import AllocateByAction from './allocate/AllocateByAction'
export default {
  name: 'ViewByServer',
  components: {
    card: Card,
    'footer-statis': FooterStatis,
    'allocate-by-action': AllocateByAction
  },
  data() {
    return {}
  },
  props: {
    deployment: {
      type: Array,
      required: true
    },
    data: {
      type: Array,
      required: true
    },
    displayMode: {
      type: String,
      required: false
    }
  },
  computed: {
    /**
     * 将原始数据转换成Card渲染所需数据结构
     * [
     *  {
     *   title: {
     *     titleText,
     *     titileIcon,
     *     event
     *   },
     *   badgeList:[
     *     {
     *       text,
     *       colorType,
     *       event: {
     *         ip,
     *         acitonID,
     *         status
     *       }
     *     }
     *   ],
     *   badgeGroupList:[
     *     {
     *       groupName,
     *       items: [
     *         {
     *           text,
     *           colorType,
     *           event
     *         }
     *       ]
     *     }
     *   ],
     *   footer: {
     *     statis: {
     *       total,
     *       running,
     *       disabled,
     *       starting,
     *       failed
     *     }
     *   }
     *  }
     * ]
     */
    cardList() {
      let actionPoint2ip2instances = new Map()
      let actionPoint2Action = new Map()
      let ip2Agent = new Map()
      for (let deployOnAgent of this.data) {
        ip2Agent.set(deployOnAgent.ip, deployOnAgent)
        for (let action of deployOnAgent.actions) {
          actionPoint2Action.set(action.actionPoint, {
            actionName: action.actionName,
            pluginCode: action.pluginCode
          })
          let ip2instances = actionPoint2ip2instances.get(action.actionPoint)
          if (!ip2instances) {
            ip2instances = new Map()
            actionPoint2ip2instances.set(action.actionPoint, ip2instances)
          }
          let instances = ip2instances.get(deployOnAgent.ip)
          if (!instances) {
            instances = []
            ip2instances.set(deployOnAgent.ip, instances)
          }
          instances.push(action)
        }
      }
      let ret = []
      let seq = 1
      for (let entry0 of actionPoint2ip2instances.entries()) {
        let actionPoint = entry0[0]
        let obj = actionPoint2Action.get(actionPoint)
        let title = {
          seq,
          titleText: obj.actionName,
          titleIcon: 'el-icon-document',
          event: {
            actionPoint,
            actionName: obj.actionName
          }
        }
        let badgeList = []
        let badgeGroupList = []
        let statis = {
          total: 0,
          running: 0,
          disabled: 0,
          failed: 0
        }
        for (let entry1 of entry0[1].entries()) {
          let ip = entry1[0]
          let agent = ip2Agent.get(ip)
          let instances = entry1[1]
          let items = []
          for (let instance of instances) {
            let text = instance.port > 0 ? ip + ':' + instance.port : ip
            let event = {
              ip,
              agentPort: agent.agentPort,
              agentStatus: agent.agentStatus,
              __view_status: instance.__view_status,
              ...instance
            }
            badgeList.push({
              text,
              colorType: instance.__view_status,
              event
            })
            items.push({
              text,
              colorType: instance.__view_status,
              event
            })
            let statisForThisIp = calcActionStatis(instances)
            for (let k in statis) {
              statis[k] += statisForThisIp[k]
            }
          }
          badgeGroupList.push({
            groupName: ip,
            items
          })
        }
        ret.push({
          title,
          badgeList,
          badgeGroupList,
          footer: {
            statis
          }
        })
        seq++
      }
      return ret
    }
  },
  methods: {
    /**
     * 将数据结构调整成以服务为维度显示的结构
     */
    __adjustStructureToActionView(data) {
      let map = new Map()
      for (let val of data) {
        for (let action of val.actions) {
          let instances = map.get(action.actionPoint)
          if (!instances) {
            instances = []
            map.set(action.actionPoint, instances)
          }
          let instance = {
            ...action,
            ...{
              ip: val.ip
            }
          }
          instances.push(instance)
        }
      }
      return Array.from(map.keys())
        .sort()
        .map(k => {
          let instances = map.get(k)
          let firstInstance = instances[0]
          return {
            actionPoint: k,
            actionName: firstInstance.actionName,
            instances
          }
        })
    },
    onAllocClick(action) {
      this.$refs.allocByAction.open(this.deployment, action)
    },
    onAllocSuccess() {
      this.$emit('alloc-success')
    }
  }
}
</script>
<style lang="scss">
.view-by-action {
  .footer-statis {
    float: right;

    > span {
      margin-right: 15px;
    }

    .iconfont {
      font-size: 10px;
    }
  }
}
</style>

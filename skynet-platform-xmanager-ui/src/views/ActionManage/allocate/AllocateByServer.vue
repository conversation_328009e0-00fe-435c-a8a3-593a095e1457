<template>
  <el-dialog
    v-if="dialogVisible"
    :visible.sync="dialogVisible"
    width="1100px"
    :close-on-click-modal="false"
    :destroy-on-close="true"
    @close="onDialogClose"
    :title="title"
  >
    <div class="body" v-loading="loading" :element-loading-text="$t('459')">
      <transfer
        ref="allocByServerTransfer"
        v-model="allocations"
        :candidates="candidates"
        :originAllocations="originAllocations"
        :leftPlaceHolder="$t('471')"
        :draggable="true"
        :labelSelectable="labelSelectable"
      />
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="mini" icon="el-icon-check" type="primary" @click="onConfirm">{{ $t('75') }}</el-button>
      <el-button size="mini" icon="el-icon-close" @click="dialogVisible = false">{{ $t('76') }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
import locale from '@/i18n/index.js'

import transfer from './Transfer'
import { deepCopy } from '@/common/util'
import { simplifyAgentDeploy } from './auxiliary'
export default {
  name: 'AllocateByServer',
  components: {
    transfer: transfer
  },
  props: [],
  data() {
    return {
      loading: false,
      dialogVisible: false,
      ip: null,
      deployment: null,
      // 服务部署拓扑
      actionDefinitions: [],
      // 所有的服务定义
      allocations: [],
      // 已分配数据
      originAllocations: [],
      // 原始已分配数据
      candidates: [],
      // 候选数据
      labelSelectable: false
    }
  },
  computed: {
    title() {
      return `${locale.t('472')}${this.labelSelectable ? locale.t('474') : locale.t('475')} [${this.ip}${locale.t('473')}`
    }
  },
  methods: {
    open(ip, deployment) {
      let thisVue = this
      this.ip = ip
      this.deployment = deployment
      let actionDefinitions = this.$store.state.skynet.actionDefinitions
      if (actionDefinitions) {
        this.actionDefinitions = actionDefinitions
        this.generateTransferData()
      } else {
        this.loading = true
        this.$api.definition
          .getActionDefinitions()
          .then(v => {
            thisVue.actionDefinitions = v
            thisVue.generateTransferData()
          })
          .catch(() => {
            this.$message({
              message: locale.t('465'),
              type: 'error',
              duration: 3000
            })
          })
          .finally(() => {
            thisVue.loading = false
          })
      }
      this.dialogVisible = true
    },
    getNodeSelector(tags) {
      // 将服务定义中的标签转换为 nodeSelector
      // 只保留标签格式为 xx=yy 的
      let nodeSelector = {}
      if (tags) {
        for (let tag of tags) {
          let ss = tag.split('=')
          if (ss.length == 2) {
            nodeSelector[ss[0]] = ss[1]
          }
        }
      }
      return nodeSelector
    },
    generateTransferData() {
      let allocationMap = new Map()
      for (let agentDeploy of this.deployment) {
        if (this.ip === agentDeploy.ip) {
          // 是 kubernetes 显示标签配置
          this.labelSelectable = agentDeploy.agentType === 'kubernetes'
          for (let instance of agentDeploy.actions) {
            let allocObj = allocationMap.get(instance.actionPoint)
            if (!allocObj) {
              allocObj = {
                key: instance.actionPoint,
                text: instance.actionName,
                num: '0',
                replicas: instance.replicas,
                nodeSelector: instance.nodeSelector,
                enabled: instance.enabled,
                pluginCode: instance.pluginCode,
                pluginName: instance.pluginName
              }
              allocationMap.set(instance.actionPoint, allocObj)
            }
            allocObj.num = '' + (parseInt(allocObj.num) + 1)
          }
          break
        }
      }
      let allocations = Array.from(allocationMap.values())
      let candidates = []
      if (this.actionDefinitions) {
        for (let actionDef of this.actionDefinitions) {
          candidates.push({
            key: actionDef.actionPoint,
            text: actionDef.actionName,
            groupKey: actionDef.pluginCode,
            groupName: actionDef.pluginName,
            nodeSelector: this.getNodeSelector(actionDef.tags),
            pluginCode: actionDef.pluginCode,
            pluginName: actionDef.pluginName,
            replicas: actionDef.replicas,
            instances: actionDef.instances
          })
        }
      }
      this.candidates = candidates
      this.allocations = allocations
      this.originAllocations = deepCopy(allocations)
    },
    onCancel() {
      this.dialogVisible = false
    },
    onDialogClose() {
      this.$refs.allocByServerTransfer.close()
    },
    onConfirm() {
      // 提取所有已分配服务的启用状态
      let actionPoint2Enabled = new Map()
      for (let agentDeploy of this.deployment) {
        if (this.ip === agentDeploy.ip) {
          let simplifiedAgentDeploy = simplifyAgentDeploy(agentDeploy)
          for (let info of simplifiedAgentDeploy.actions) {
            actionPoint2Enabled.set(info.actionPoint, info.enabled)
          }
        }
      }
      let dtoList = this.allocations.map((v, index) => {
        let enabled = actionPoint2Enabled.get(v.key)
        // 新增服务的状态为enabled
        enabled = enabled === undefined ? true : enabled
        return {
          actionPoint: v.key,
          num: parseInt(v.num),
          replicas: parseInt(v.replicas),
          order: index,
          enabled,
          nodeSelector: v.nodeSelector
        }
      })
      let __this = this
      this.$api.deployment
        .updateDeploymentByServer(this.ip, dtoList, true)
        .then(() => {
          __this.$message({
            type: 'success',
            message: locale.t('468')
          })
          __this.dialogVisible = false
          __this.$emit('success')
        })
        .catch(() => {
          __this.$message({
            type: 'error',
            message: locale.t('469')
          })
        })
    }
  }
}
</script>

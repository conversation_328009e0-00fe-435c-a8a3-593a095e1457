import locale from '@/i18n/index.js'
/**
 * 将部署拓扑数据结构简化成[{ip,actions:[{actionPoint,num,enabled}]}]的形式,并保持服务顺序不变
 */
let simplifyClusterDeployment = function(deployment) {
  let ret = []
  for (let agentDeploy of deployment) {
    let simplifiedAgentDeploy = simplifyAgentDeploy(agentDeploy)
    ret.push(simplifiedAgentDeploy)
  }
  return ret
}

/**
 * 将server的部署数据结构简化成{ip, actions:[{actionPoint,num,enabled}]}的形式,并保持服务顺序不变
 */
let simplifyAgentDeploy = function(agentDeploy) {
  let map = new Map()
  for (let actionOnThisAgent of agentDeploy.actions) {
    let info = map.get(actionOnThisAgent.actionPoint)
    if (!info) {
      info = {
        num: 0,
        enabled: actionOnThisAgent.enabled
      }
    }
    info.num = info.num + 1
    map.set(actionOnThisAgent.actionPoint, info)
  }
  let actions = []
  for (let actionOnThisAgent of agentDeploy.actions) {
    let info = map.get(actionOnThisAgent.actionPoint)
    if (info) {
      actions.push({
        actionPoint: actionOnThisAgent.actionPoint,
        num: info.num,
        enabled: info.enabled
      })
      map.delete(actionOnThisAgent.actionPoint)
    }
  }
  return {
    ip: agentDeploy.ip,
    actions
  }
}
export { simplifyClusterDeployment, simplifyAgentDeploy }

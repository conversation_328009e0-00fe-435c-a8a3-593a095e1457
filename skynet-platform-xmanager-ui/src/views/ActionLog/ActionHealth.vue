<template>
  <log-view :param="param"></log-view>
</template>
<script>
import locale from '@/i18n/index.js'

import LogView from '../log/LogView'
import { encryptUrl } from '@/utils/auth'
export default {
  name: 'ActionHealth',
  components: {
    LogView
  },
  data() {
    return {
      param: {}
    }
  },
  created() {
    let actionID = this.$route.params.aid
    let ip = this.$route.params.ip
    let port = this.$route.query.port
    let proxyUri = `ws://${ip}:${port}/skynet/agent/health/${actionID}`
    let eUrl = encryptUrl(proxyUri)
    let logUri = `ws://${window.location.host}${window.location.pathname}skynet/proxy/ws?u=${eUrl}`
    this.param = {
      logUri,
      title: `[${ip}][${actionID}${locale.t('437')}`
    }
  }
}
</script>

import serverManage from './server-manage'
import menu from './menu'

const zhL<PERSON>ale = {
  menu,
  serverManage,
  '0': '节点列表获取失败',
  '1': ' SSH连接成功',
  '2': 'Kubernetes连接成功',
  '3': ' SSH连接失败',
  '4': 'Kubernetes连接失败',
  '5': '节点创建成功',
  '6': '节点创建失败',
  '7': '节点信息更新成功',
  '8': '节点信息更新失败',
  '9': '在线',
  '10': '离线',
  '11': '分发中',
  '12': '核 | ',
  '13': 'GB内存',
  '14': '节点',
  '15': '未知',
  '16': '密码修改成功',
  '17': '集群级属性更新成功',
  '18': '集群级属性更新失败',
  '19': '集群级日志级别配置更新成功',
  '20': '集群级日志级别配置更新失败',
  '21': '配置保存成功',
  '22': '配置保存失败',
  '23': '顺序设置成功',
  '24': '顺序设置失败',
  '25': '配置导入成功',
  '26': '配置导入失败',
  '27': '服务创建成功',
  '28': '服务创建失败',
  '29': '服务更新成功',
  '30': '服务更新失败',
  '31': '服务定义导入成功',
  '32': '服务定义导入失败',
  '33': '类型获取失败',
  '34': '操作失败',
  '35': '查询失败',
  '36': '删除失败',
  '37': '应用系统创建成功',
  '38': '应用系统创建',
  '39': '服务信息更新成功',
  '40': '服务信息更新失败',
  '41': '系统级属性更新成功',
  '42': '系统级属性更新失败',
  '43': '系统级日志级别配置更新成功',
  '44': '系统级日志级别配置更新失败',
  '45': '代理请求失败',
  '46': '文件删除成功',
  '47': '文件删除失败',
  '48': '服务标签更新成功',
  '49': '服务标签更新失败',
  '50': '服务器标签更新成功',
  '51': '服务器标签更新失败',
  '52': '应用展示顺序调整成功',
  '53': '应用展示顺序调整失败',
  '54': '节点展示顺序调整成功',
  '55': '节点展示顺序调整失败',
  '56': '服务展示顺序调整成功',
  '57': '服务展示顺序调整失败',
  '58': ' [请求失败]',
  '59': ' [无法识别的API响应]',
  '60': ' [错误码: ',
  '61': '会话过期，即将跳转到登陆页面',
  '62': '复制到剪切板',
  '63': '新建同级',
  '64': '编辑',
  '65': '删除',
  '66': '提示',
  '67': '成就客户',
  '68': '创新坚守',
  '69': '团队协作',
  '70': '简单真诚',
  '71': '专业敬业',
  '72': '担当奋进',
  '73': '或者 滑动滑块到最右端以确定',
  '74': '已确定',
  '75': '确定',
  '76': '取消',
  '77': '男',
  '78': '女',
  '79': '性别',
  '80': '点击，或拖动图片至此处',
  '81': '正在上传……',
  '82': '浏览器不支持该功能，请使用IE10以上或其他现在浏览器！',
  '83': '上传成功',
  '84': '图片上传失败',
  '85': '头像预览',
  '86': '关闭',
  '87': '上一步',
  '88': '保存',
  '89': '仅限图片格式',
  '90': '单文件大小不能超过 ',
  '91': '图片最低像素为（宽*高）：',
  '92': '點擊，或拖動圖片至此處',
  '93': '正在上傳……',
  '94': '瀏覽器不支持該功能，請使用IE10以上或其他現代瀏覽器！',
  '95': '上傳成功',
  '96': '圖片上傳失敗',
  '97': '頭像預覽',
  '98': '關閉',
  '99': '僅限圖片格式',
  '100': '單文件大小不能超過 ',
  '101': '圖片最低像素為（寬*高）：',
  '102': 'アップロード中...',
  '103': 'このブラウザは対応されていません。IE10+かその他の主要ブラウザをお使いください。',
  '104': 'アップロード成功',
  '105': 'アップロード失敗',
  '106': '閉じる',
  '107': '戻る',
  '108': '画像のみ',
  '109': '画像サイズが上限を超えています。上限: ',
  '110': '画像が小さすぎます。最小サイズ: ',
  '111': '按enter键添加',
  '112': '新增',
  '113': '确定要删除标签【',
  '114': '】吗？',
  '115': '已存在',
  '116': '不支持中文！',
  '117': '请输入',
  '118': '请选择',
  '119': '搜索',
  '120': '重置',
  '121': '输入搜索条件',
  '122': '选择时间',
  '123': '请',
  '124': '+ 选择标签',
  '125': '选择标签',
  '126': '新增标签',
  '127': '输入标签名称后回车',
  '128': '标签删除后不可恢复,确定删除吗？',
  '129': '已存在同名标签!',
  '130': ' 将文件拖到此处，或',
  '131': '点击上传',
  '132': ' Drag或',
  '133': '管理员',
  '134': '退出登录',
  '135': '控制台启动时间:',
  '136': '控制台启动时长:',
  '137': '构建分支:',
  '138': '构建提交:',
  '139': '构建序号:',
  '140': '构建时间:',
  '141': ' 版本: ',
  '142': ' 语言：',
  '143': '中文',
  '144': '英文',
  '145': '未知版本',
  '146': '新窗口打开',
  '147': '刷新',
  '148': '关闭其他',
  '149': '关闭所有',
  '150': '控制台',
  '151': '服务管理',
  '152': '节点管理',
  '153': '服务定义',
  '154': '配置管理',
  '155': '集群监控',
  '156': '/redirect/grafana/cluster?tabTitle=集群监控&grafanaUrlPathInRoute=',
  '157': '辅助工具',
  '158': '备份管理',
  '159': '节点详情 / ',
  '160': '主机详情 / ',
  '161': '容器组详情 / ',
  '162': '部署详情 / ',
  '163': '守护进程集详情 / ',
  '164': '有状态副本集详情 / ',
  '165': 'Skynet控制台日志',
  '166': 'SKYNET管理控制台',
  '167': 'Skynet服务控制台',
  '168': '日',
  '169': '一',
  '170': '二',
  '171': '三',
  '172': '四',
  '173': '五',
  '174': '六',
  '175': '刚刚',
  '176': '分钟前',
  '177': '小时前',
  '178': '1天前',
  '179': '月',
  '180': '时',
  '181': '分',
  '182': '天',
  '183': '小时',
  '184': '分钟',
  '185': '秒',
  '186': '已调度',
  '187': '已初始化',
  '188': '容器已就绪',
  '189': '容器组已就绪',
  '190': '未调度',
  '191': '未初始化',
  '192': '容器未就绪',
  '193': '容器组未就绪',
  '194': '容器已完成',
  '195': '容器组已完成',
  '196': '初始化容器',
  '197': '工作容器',
  '198': '集群属性配置',
  '199': '集群日志配置',
  '200': '导入',
  '201': '应用系统',
  '202': '加载应用系统列表',
  '203': '请输入关键词',
  '204': '置顶',
  '205': '置底',
  '206': '查看',
  '207': '导出',
  '208': '选择',
  '209': '排序',
  '210': '新增应用系统',
  '211': '导出应用系统配置',
  '212': '删除选中应用系统',
  '213': '加载中',
  '214': '服务列表',
  '215': '资源仓库',
  '216': '系统属性',
  '217': '系统日志',
  '218': '所有应用系统',
  '219': '成功删除',
  '220': '个应用系统，',
  '221': '个应用系统删除失败',
  '222': '应用系统删除成功',
  '223': '确定删除以下应用系统?',
  '224': '请先选择要导出的系统',
  '225': '请拖拽列表行调整顺序',
  '226': '选择类型',
  '227': '新建',
  '228': '序号',
  '229': '服务名',
  '230': '编码',
  '231': '类型',
  '232': '协议',
  '233': '端口',
  '234': '分类标签',
  '235': '日志采集',
  '236': '是',
  '237': '否',
  '238': '监控采集',
  '239': '操作',
  '240': ' 编辑 ',
  '241': '复制',
  '242': ' / 编辑',
  '243': '服务定义 / ',
  '244': ' / 创建服务',
  '245': '创建服务',
  '246': '个服务，',
  '247': '个服务删除失败',
  '248': '服务删除成功',
  '249': '确定删除以下服务?',
  '250': '请先选择要导出的服务',
  '251': '集群-日志级别配置',
  '252': '日志级别:OFF,ERROR,WARN,INFO,DEBUG,TRACE',
  '253': '集群-属性配置',
  '254': '可用系统变量占位符号或者自定义的属性引用，如 ${my.prop.key}',
  '255': '集群级日志级别',
  '256': '获取集群级日志级别配置失败',
  '257': '集群级属性',
  '258': '获取集群属性失败',
  '259': '工具',
  '260': '系统级日志级别',
  '261': '获取系统级日志级别配置失败',
  '262': '系统级属性',
  '263': '获取系统属性失败',
  '264': '基本配置',
  '265': '关联文件',
  '266': '模板文件',
  '267': '功能选项',
  '268': '依赖服务',
  '269': '属性配置',
  '270': '日志配置',
  '271': '保存并关闭',
  '272': '还原',
  '273': '启用设定首页',
  '274': 'URL路径',
  '275': '启用日志收集',
  '276': '切换回查看状态将丢失所有修改, 是否继续?',
  '277': '基本信息',
  '278': '系统名称:',
  '279': '系统编码:',
  '280': '服务名称:',
  '281': '例如：[AST]中文听写服务[Rest]',
  '282': '服务编码:',
  '283': '例如: rest-ast-v20 【命名规范：由 数字英文-]等字符组成】[引用变量${SKYNET_ACTION_CODE}]',
  '284': '服务描述:',
  '285': '例如：中文语音听写服务',
  '286': '服务标签:',
  '287': '服务类型:',
  '288': '服务协议:',
  '289': '服务端口:',
  '290': '扩展端口:',
  '291': '多个端口可用半角下逗号分隔',
  '292': '启停配置',
  '293': '工作目录:',
  '294': '例如：/iflytek/server/skynet [引用变量${WORK_HOME}]',
  '295': '启动命令:',
  '296': '请输入服务启动命令（不支持后台方式启动，如nohup , sh * &）',
  '297': '环境变量:',
  '298': '如JAVA_HOME=/usr/java/jdk，多个环境变量用换行隔开',
  '299': '退出信号:',
  '300': '选择Linux信号值',
  '301': 'JAR文件名:',
  '302': 'JVM选项:',
  '303': '如-Xms512M -Xmx1G -agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=5005 ',
  '304': 'Context配置:',
  '305': '命令行参数:',
  '306': '镜像名称:',
  '307': '请输入镜像名，如 hub.iflytek.com/turing-common/edu_base:1.0.0',
  '308': 'Docker选项:',
  '309': 'docker create [OPTIONS] IMAGE [COMMAND] [ARG...] 中的OPTIONS部分（建议每行一个）， 如-v /data/vol/mypath:/path-in-container -p 18080:8080',
  '310': '运行参数:',
  '311': 'docker create [OPTIONS] IMAGE [COMMAND] [ARG...] 中的[COMMAND] [ARG...]部分',
  '312': '健康检查',
  '313': '检查方式:',
  '314': '检查PID',
  '315': '网络协议',
  '316': 'URL路径:',
  '317': '初始延时[秒]:',
  '318': '检查间隔[秒]:',
  '319': '超时时间[秒]:',
  '320': '失败次数:',
  '321': '请选择所属系统',
  '322': '请填写服务编码',
  '323': '编码支持英文字母、数字、横线、下划线、点号的组合',
  '324': '请填写服务名称',
  '325': '请填写服务端口',
  '326': '必须是非负整型',
  '327': '请填写工作目录',
  '328': '请填写启动命令',
  '329': '请选择退出信号',
  '330': '请填写jar文件名',
  '331': '请填写URL路径',
  '332': '请填写检查延迟时间',
  '333': '必须是整型',
  '334': '请填写检查周期',
  '335': '请填写超时时间',
  '336': '请填写重试次数',
  '337': '配置块设置',
  '338': '加载配置块列表',
  '339': '未分配',
  '340': '全部移到右侧',
  '341': '全部移到左侧',
  '342': '已分配(优先级从高到低，序号1最高)',
  '343': '增!',
  '344': '减!',
  '345': '依赖服务设置',
  '346': '启动依赖：依赖的服务未启动，本服务将启动失败；调用依赖：主要显示服务之间的调用关系；',
  '347': '启动依赖：',
  '348': ' 暂无启动依赖 ',
  '349': '调用依赖：',
  '350': ' 没有调用依赖 ',
  '351': '加载依赖服务列表',
  '352': '启动依赖',
  '353': '调用依赖',
  '354': '已分配',
  '355': '说明：模板文件内容会在做属性变量替换后写入如下配置的文件路径',
  '356': '文件路径:',
  '357': '文件编码:',
  '358': '默认：utf-8',
  '359': '文件权限:',
  '360': '默认：读写',
  '361': '归属用户:',
  '362': '默认：agent启动用户',
  '363': '文件内容:',
  '364': '展开',
  '365': '收起',
  '366': '移除此模板文件',
  '367': '文件路径不能为空',
  '368': '此配置仅针对使用Logback的SpringBoot项目生效,日志级别:OFF,ERROR,WARN,INFO,DEBUG,TRACE',
  '369': '服务级日志级别(优先级最高)',
  '370': '可用系统变量占位符号或者自定义的属性引用，如 ${my.prop.key}；属性的优先级从高到低',
  '371': '服务级属性(优先级最高)',
  '372': '配置块：',
  '373': '加载仓库文件列表',
  '374': '说明：关联的文件会从仓库下载到目标目录下；文件名被红色边框包围说明该文件在资源仓库中不存在',
  '375': '文件名称:',
  '376': '请输入文件名',
  '377': '目标目录:',
  '378': '移除此关联文件',
  '379': '文件名称不能为空',
  '380': '目标目录不能为空',
  '381': '所在Agent IP',
  '382': '本服务端口',
  '383': '扩展端口[1开始]',
  '384': '工作目录',
  '385': 'Skynet根目录',
  '386': 'Skynet临时目录',
  '387': '当前ZK地址',
  '388': '当前ZK JAAS验证配置文件',
  '389': '集群名-ZK根路径',
  '390': '控制台地址',
  '391': '本机Agent地址',
  '392': '系统编码',
  '393': '服务编码',
  '394': '服务坐标',
  '395': '服务实例标识',
  '3951': '服务实例序号',
  '396': '应用根目录',
  '397': '应用HOME目录',
  '398': '扩展配置默认目录',
  '399': '配置服务地址',
  '400': '本地配置文件',
  '401': 'Skynet日志配置文件',
  '402': '分级配置地址',
  '403': '预定义环境变量',
  '404': '单击复制变量名',
  '405': '样例',
  '406': '样例 : ',
  '407': '例如: turing-tts 支持英文字母、数字、横线、下划线的组合',
  '408': '系统名称',
  '409': '例如: AI能力-语音合成',
  '410': '系统描述',
  '411': '系统版本',
  '412': '例如: v2.1.1008',
  '413': '请填写系统编码',
  '414': '支持英文字母、数字、横线、下划线组合,长度不超过80个字符',
  '415': '请填写系统名称',
  '416': '长度不超过80个字符',
  '417': '长度不超过200个字符',
  '418': '长度不超过100个字符',
  '419': '应用系统 / 新建',
  '420': ' / 查看',
  '421': '此配置仅针对使用Logback的SpringBoot项目生效, 日志级别:OFF,ERROR,WARN,INFO,DEBUG,TRACE',
  '422': '显示MD5文件',
  '423': '打包下载',
  '424': '资源文件目录: ',
  '425': ' [*.md5文件由系统自动生成,不需要上传]。',
  '426': '文件名',
  '427': '文件大小',
  '428': '上传时间',
  '429': '修改时间',
  '430': '下载',
  '431': '将文件拖到此处，或',
  '432': "'最近一次上传还未完成",
  '433': "'等",
  '434': '个文件最近一次上传还未完成',
  '435': '确定覆盖以下文件吗?',
  '436': '确认删除以下文件?',
  '437': '] 健康日志：',
  '438': '] 标准输出：',
  '439': '] 服务日志：',
  '440': '视图状态',
  '441': '控制台日志',
  '442': '重启',
  '443': '启用',
  '444': '禁用',
  '445': '启停-启用服务',
  '446': '启停-禁用服务',
  '447': '请选择服务实例',
  '448': '不支持的操作',
  '449': '服务',
  '450': '成功, 2秒后自动刷新视图',
  '451': '服务重启成功, 2秒后自动刷新视图',
  '452': '服务重启失败, 2秒后自动刷新视图',
  '453': '个节点的服务重启失败, 2秒后自动刷新视图',
  '454': '部分节点服务重启成功: %s',
  '455': '无法识别的响应',
  '456': '失败, 2秒后自动刷新视图',
  '457': '以下服务?',
  '458': ' 上的 ',
  '459': '加载服务定义列表',
  '460': '选择服务',
  '461': '选择服务器标签',
  '462': '为 ',
  '463': ' 分配服务实例',
  '464': '服务分配',
  '465': '服务列表获取失败',
  '466': '核|',
  '467': '[无服务器标签]',
  '468': '服务分配成功,2秒后自动刷新视图',
  '469': '服务分配失败',
  '470': '服务分配部分节点成功,2秒后自动刷新视图',
  '471': '选择应用系统',
  '472': '为',
  '473': '] 分配服务',
  '474': '集群',
  '475': '服务器',
  '476': ' 个标签',
  '477': '副本',
  '4777': '副本数',
  '478': '实例',
  '479': '[已禁用]',
  '480': '显示',
  '481': '分组',
  '482': '平铺',
  '483': '折叠',
  '484': '多选',
  '485': '全选',
  '486': '服务数量',
  '487': '正常',
  '488': '异常',
  '489': '停止',
  '490': '启动中',
  '491': '启动失败',
  '492': '查看-服务定义',
  '493': '查看-服务属性',
  '494': '查看-启动详情',
  '495': '查看-工作负载',
  '496': '查看-健康检测',
  '497': '查看-服务日志',
  '498': '查看-标准输出',
  '499': '查看-监控图表',
  '500': '启停-重启服务',
  '501': '健康检测输出 / ',
  '502': '标准输出 / ',
  '503': '服务日志 / ',
  '504': 'Skynet-监控/仪表盘服务 未启动',
  '505': '服务监控/',
  '506': '启动详情',
  '507': '启动命令',
  '508': '查看日志',
  '509': '复制命令',
  '510': '详细数据',
  '511': '失败原因',
  '512': '获取服务启动详情失败',
  '513': '单击复制',
  '514': '服务标识',
  '515': '服务类型',
  '516': '服务地址',
  '517': 'DNS地址',
  '518': '启动时间',
  '519': '运行时长',
  '520': '服务首页',
  '521': '服务属性',
  '522': '工作负载',
  '523': '服务日志',
  '524': '标准输出',
  '525': '健康检测',
  '526': '监控图表',
  '527': '请求执行成功:',
  '528': '请求执行失败',
  '529': '确定执行[',
  '530': ']操作吗？',
  '531': '获取节点详情失败',
  '532': '类型 ',
  '533': '[单击选择执行]',
  '534': '输出',
  '535': '说明: ',
  '536': '命令: ',
  '537': '概览',
  '538': '系统环境',
  '539': '主机监控',
  '540': '事件通知',
  '541': '分发日志',
  '542': ']ant-xagent@ant] 服务日志:',
  '543': '][ant-xagent@ant] 事件通知:',
  '544': '][ant-xagent@ant] 分发日志:',
  '545': '教育',
  '546': '96核 | 254GB内存 | 0xGPU',
  '547': '7天12小时5分12秒',
  '548': 'Skynet监控/主机监控',
  '549': '节点名:',
  '550': '操作系统:',
  '551': '硬件配置:',
  '552': '服务端点:',
  '553': '服务坐标:',
  '554': '备注:',
  '555': 'SSH用户:',
  '556': 'SSH端口:',
  '557': '标签:',
  '558': '版本信息',
  '559': '服务版本:',
  '560': '框架版本:',
  '561': '运行时信息',
  '562': '服务状态:',
  '563': '启动时间:',
  '564': '运行时长:',
  '565': 'ZooKeeper地址:',
  '566': '新增节点',
  '567': '编辑节点',
  '568': '节点类型',
  '569': 'IP地址',
  '570': '请输入Agent IP地址',
  '571': 'SSH端口',
  '572': '请输入SSH端口',
  '573': 'SSH用户',
  '574': '请输入SSH用户',
  '575': '请输入 ~/.kube/config 文件中的内容',
  '576': '镜像仓库地址',
  '577': '请输入镜像仓库地址',
  '578': '镜像仓库用户名',
  '579': '请输入镜像仓库用户名',
  '580': '镜像仓库密码',
  '581': '请输入镜像仓库密码',
  '582': '如果不更改密码请保持该输入为空',
  '583': 'SSH密码',
  '584': '请输入SSH密码',
  '585': '备注',
  '586': '请输入描述',
  '587': '服务器标签',
  '588': '注册时自动分发',
  '589': '自动安装docker',
  '590': '测试SSH连接',
  '591': '测试Kubernetes连接',
  '592': 'IP地址格式错误',
  '593': '该节点已存在',
  '594': '端口范围0~65535',
  '595': '用户名不能超过32个字符',
  '596': '备注不 能超过70个字符',
  '597': '请输入 KubeConfig 内容',
  '598': '列表加载中',
  '599': '状态:',
  '600': '选择状态',
  '601': 'IP地址:',
  '602': '请输入IP',
  '603': '添加',
  '604': '启动',
  '605': '更新版本',
  '606': '打开终端',
  '607': '节点名',
  '608': '硬件信息',
  '609': '操作系统',
  '610': '服务版本',
  '611': '打开终端 ',
  '612': '无法删除运行中的节点，请先停止节点',
  '613': '只能更新离线状态的节点，请等待更新结束或者停止在线节点',
  '614': '请选择节点',
  '615': '更新',
  '616': '失败',
  '617': '成功',
  '618': '个节点，',
  '619': '个节点',
  '620': '确认',
  '621': '以下节点?',
  '622': '强制更新',
  '623': '安装Docker',
  '624': '确认更新以下节点?',
  '625': '主机',
  '626': 'CPU(已用/容量)',
  '627': '内存(已用/容量)',
  '628': '数据加载中...',
  '629': '命名空间：',
  '630': '所有',
  '631': '复制到剪贴板',
  '632': '预览精简的 YAML',
  '633': '隐藏 status 字段',
  '634': ' 选择YAML文件 ',
  '635': ' 取消 ',
  '636': ' 确定 ',
  '637': ' 编辑 YAML ',
  '638': '请输入名称',
  '639': '创建',
  '640': '名称',
  '641': '命名空间',
  '642': '不可修改',
  '643': '标签',
  '644': '已创建',
  '645': '查看YAML',
  '646': '配置字典: ',
  '647': '从 YAML 创建',
  '648': '配置字典 删除中...',
  '649': '配置字典 [ ',
  '650': ' ] 成功删除，2秒后自动刷新页面。',
  '651': '确定删除配置字典：[ ',
  '652': '最小/最大/当前副本',
  '653': '自动伸缩: ',
  '654': 'HPA自动伸缩 删除中...',
  '655': 'HPA自动伸缩 [ ',
  '656': '确定删除HPA自动伸缩：[ ',
  '657': '密文: ',
  '658': '密文配置 删除中...',
  '659': '密文配置 [ ',
  '660': '确定删除密文配置：[ ',
  '661': '自定义资源: ',
  '662': '自定义资源 删除中...',
  '663': '自定义资源 [ ',
  '664': '确定删除自定义资源：[ ',
  '665': '[工作负载-守护进程集]',
  '666': '日志/终端',
  '667': ' 调整镜像版本 ',
  '668': '更新策略',
  '669': '重启守护进程集',
  '670': '删除守护进程集',
  '671': '元数据',
  '672': '运行时',
  '673': '正在加载...',
  '674': '工作负载-守护进程集YAML加载中',
  '675': '工作负载-守护进程集: ',
  '676': '正在加载 Pod 列表...',
  '677': '工作负载-守护进程集 重启中...',
  '678': '工作负载-守护进程集 ',
  '679': ' 成功重启，2秒后自动刷新',
  '680': '确定重启 工作负载-守护进程集：[',
  '681': '工作负载-守护进程集 删除中...',
  '682': ' 成功删除',
  '683': '确定删除 工作负载-守护进程集：[',
  '684': '[工作负载-部署]',
  '685': '伸缩',
  '686': '自动伸缩[HPA] ',
  '687': '历史版本',
  '688': '重启部署',
  '689': '删除部署',
  '690': '工作负载-部署YAML加载中',
  '691': '工作负载-部署: ',
  '692': '正在加载 HPA YAML',
  '693': '编辑 HPA YAML: ',
  '694': '新建 HPA YAML: ',
  '695': '工作负载-部署 重启中...',
  '696': '工作负载-部署 ',
  '697': '确定重启 工作负载-部署：[',
  '698': '工作负载-部署 删除中...',
  '699': '确定删除 工作负载-部署：[',
  '700': '更新策略更新中',
  '701': '请选择更新策略类型',
  '702': '手工删除Pod后更新[OnDelete]',
  '703': '滚动更新[RollingUpdate]',
  '704': '更新策略：',
  '705': ' 当使用 OnDelete作为升级策略时，在创建好新的 DaemonSet 配置之后，',
  '706': ' 新的 Pod 并不会被创建，直到用户手动删除旧版本的 Pod，才触发新建操作，',
  '707': ' 即只有手工删除了 DaemonSet 创建的 Pod 副本，新的 Pod 副本才会被创建出来。',
  '708': ' 是一种平滑的更新策略，它在更新过程中保证服务的可用性。',
  '709': ' 默认选项。当使用 RollingUpdate 作为升级策略对 DaemonSet 进行更新时，',
  '710': ' 旧版本的 Pod 将被自动“杀掉”，然后自动创建新版本的 DaemonSet Pod。',
  '711': ' 整个过程与普通 Deployment 的滚动升级一样是可控的。',
  '712': '最大不可用副本数',
  '713': '最大不可用副本数[maxUnavailable]',
  '714': ' 可以是绝对值（例如：3），也可以是百分比（例如：10%）。',
  '715': ' 如果是百分比，Kubernetes 会通过向上取整的方式将其换算为绝对值。',
  '716': '更新策略更新成功',
  '717': ' 当前副本集数：',
  '718': ', 最大历史副本集数：',
  '719': '最大历史副本集数',
  '720': '创建时间',
  '721': '期望/当前/就绪',
  '722': '回滚到#',
  '723': '删除中...',
  '724': '正在调整最大历史副本数..',
  '725': '最大历史副本数成功调整为 ',
  '726': '正在加载 副本集 YAML ...',
  '727': '工作负载-副本集: ',
  '728': '确定要 删除 [',
  '729': '] 副本集吗？',
  '730': '成功删除 [',
  '731': '] 副本集',
  '732': '回滚执行中...',
  '733': '成功回滚到 [',
  '734': '重新创建[Recreate]',
  '735': '是一种更新策略，它会将旧容器组完全删除，并创建一个新的容器组。',
  '736': ' 这种更新策略不保证服务的可用性，因为在更新过程中服务可能会中断。',
  '737': '在滚动更新中，Kubernetes 会按顺序更新容器组中的每个容器，并在更新过程中检查新版本的容器是否能够正常工作。',
  '738': ' 如果新版本不能正常工作，Kubernetes 将会滚动回到旧版本。',
  '739': '总结：',
  '740': '滚动更新策略更安全，保证服务的可用性，重新创建策略更新速度快，但不保证服务的可用性。 ',
  '741': '最大超出副本数',
  '742': '最大超出副本数[maxSurge]',
  '743': ' 滚动更新过程中，可以超出期望副本数的最大值。',
  '744': ' 该取值可以是一个绝对值（例如：3），也可以是一个相对于期望副本数的百分比（例如：15%）；',
  '745': ' 如果填写百分比，则以期望副本数乘以该百分比后向上取整的方式计算对应的绝对值；',
  '746': ' 当最大不可用副本数 maxUnavailable 为 0 时，此数值（maxSurge）不能为 0；默认值为 25%。',
  '747': ' 例如：假设此值被设定为 30%，当滚动更新开始时，新的副本集（ReplicaSet）可以立刻扩容，',
  '748': ' 但是旧 Pod 和新 Pod 的总数不超过 Deployment 期待副本数（spec.repilcas）的 130%。',
  '749': ' 一旦旧 Pod 被终止后，新的副本集可以进一步扩容，但是整个滚动更新过程中，新旧 Pod 的总',
  '750': ' 数不超过 Deployment 期待副本数（spec.repilcas）的 130%。',
  '751': ' 滚动更新过程中，不可用副本数的最大值。',
  '752': ' 如果填写百分比，则以期望副本数乘以该百分比后向下取整的方式计算对应的绝对值；',
  '753': ' 当最大超出副本数 maxSurge 为 0 时，此数值（maxUnavailable）不能为 0；默认值为 25%；',
  '754': ' 例如：假设此值被设定为 30%，当滚动更新开始时，旧的副本集（ReplicaSet）可以缩容到期望',
  '755': ' 副本数的 70%；在新副本集扩容的过程中，一旦新的 Pod 已就绪，旧的副本集可以进一步缩容，',
  '756': ' 整个滚动更新过程中，确保新旧就绪副本数之和不低于期望副本数的 70%。 ',
  '757': '调整镜像版本',
  '758': '调整中',
  '759': '容器类型',
  '760': '镜像',
  '761': '当前版本',
  '762': '新版本',
  '763': '请输入新版本',
  '764': '版本号没有修改',
  '765': '将要调整 ',
  '766': ' 个容器的镜像版本，是否继续？',
  '767': '确认调整镜像版本',
  '768': '容器日志',
  '769': '终端访问',
  '770': '关闭窗口',
  '771': '副本数更新中',
  '772': '当前副本数：',
  '773': '调整为：',
  '774': '副本数成功更新为 ',
  '775': '确认回滚',
  '776': '当前版本 #',
  '777': '目标版本 #',
  '778': ' 如果 StatefulSet 的 .spec.updateStrategy.type 字段被设置为 OnDelete，',
  '779': ' 当您修改 .spec.template 的内容时，StatefulSet Controller 将不会自动更新其 Pod。',
  '780': ' 您必须手工删除 Pod，此时 StatefulSet Controller 在重新创建 Pod 时，',
  '781': ' 使用修改过的 .spec.template 的内容创建新 Pod。',
  '782': ' .spec.updateStrategy.type 字段的默认值是 RollingUpdate，',
  '783': ' 该策略为 StatefulSet 实现了 Pod 的自动滚动更新。',
  '784': ' 在用户更新 StatefulSet 的 .spec.tempalte 字段时，StatefulSet Controller 将自动地删除并重建 StatefulSet 中的每一个 Pod。',
  '785': ' 处理顺序如下： ',
  '786': ' 从序号最大的 Pod 开始，逐个删除和更新每一个 Pod，直到序号最小的 Pod 被更新 ',
  '787': ' 当正在更新的 Pod 达到了 Running 和 Ready 的状态之后，才继续更新其前序 Pod ',
  '788': '分片',
  '789': ' 序号大于等于此数字的容器组将被更新，小于此容器组的则不会被更新。',
  '790': ' 如果分片为 0，则所有容器组都将被更新。',
  '791': ' 如果分片为 3，则序号大于等于 3 的容器组将被更新，序号为 1、2 的容器组则保持不变。',
  '792': '删除工作负载确认：',
  '793': '工作负载删除中',
  '794': '资源类型：',
  '795': '名称空间：',
  '796': '资源名称：',
  '797': '强制删除：',
  '798': '确认删除',
  '799': '请输入资源名称,确认删除',
  '800': '容器标识：',
  '801': '镜像名称：',
  '802': '抓取策略：',
  '803': '启动时间：',
  '804': '资源占用：',
  '805': ' / 内存 ',
  '806': '容器端口',
  '807': '宿主机端口',
  '808': '命令',
  '809': '/  参数',
  '810': '命令参数',
  '811': '环境变量',
  '812': '数据卷挂载',
  '813': '只读',
  '814': '读写',
  '815': '数据卷',
  '816': '资源请求',
  '817': '资源限制',
  '818': '资源请求/限制',
  '819': '启动检查探针',
  '820': '就绪检查探针',
  '821': '存活检查探针',
  '822': '探针',
  '823': '[未设置]',
  '824': '参数',
  '825': '内存',
  '826': '未配置',
  '827': '延迟时间',
  '828': '秒 ',
  '829': ' 未配置 ',
  '830': '执行频率',
  '831': '超时时间',
  '832': '健康阈值',
  '833': '不健康阈值',
  '834': '标签: ',
  '835': '注解: ',
  '836': '工作负载名称',
  '837': '工作负载类型',
  '838': '[容器组信息]',
  '839': '删除容器组',
  '840': '容器组: ',
  '841': '工作负载-容器组 删除中...',
  '842': '工作负载-容器组 ',
  '843': '确定删除工作负载-容器组：[',
  '844': '状态: ',
  '845': '消息：',
  '846': '原因：',
  '847': ' 正在删除此Pod。删除时间：',
  '848': ' 秒',
  '849': '容器列表',
  '850': '相关事件',
  '851': '[无]',
  '852': '容器组IP',
  '853': '所在主机',
  '854': '创建归属',
  '855': ' 容器日志：',
  '856': '[副本集]',
  '857': '已创建:',
  '858': ' 创建时间：',
  '859': '期望:',
  '860': '当前:',
  '861': '就绪:',
  '862': '删除 ',
  '863': '副本集管理的容器组：',
  '864': '[容器组]',
  '865': '已启动: ',
  '866': ' 启动时间:',
  '867': '加载容器组列表...',
  '868': '加载工作负载-副本集YAML',
  '869': '副本集删除中...',
  '870': '副本集 ',
  '871': '确定删除副本集：[',
  '872': '加载工作负载-容器组YAML',
  '873': '工作负载-容器组: ',
  '874': '容器组删除中...',
  '875': '容器组 ',
  '876': '确定删除容器组：[',
  '877': '[工作负载-有状态副本集]',
  '878': '重启有状态副本集',
  '879': '删除有状态副本集',
  '880': '工作负载-有状态副本集YAML加载中',
  '881': '工作负载-有状态副本集: ',
  '882': '工作负载-有状态副本集 重启中...',
  '883': '工作负载-有状态副本集 ',
  '884': '确定重启 工作负载-有状态副本集：[',
  '885': '工作负载-有状态副本集 删除中...',
  '886': '确定删除 工作负载-有状态副本集：[',
  '887': 'K8S集群 [',
  '888': '集群概览',
  '889': '负载概览',
  '890': '容器组',
  '891': '部署',
  '892': '守护进程集',
  '893': '有状态副本集',
  '894': '定时任务',
  '895': '任务',
  '896': '配置',
  '897': '配置字典',
  '898': '密文',
  '899': '自动伸缩',
  '900': '网络',
  '901': '端点',
  '902': '路由',
  '903': '自定义资源',
  '904': '命名空间: ',
  '905': '命名空间 删除中...',
  '906': '命名空间 [ ',
  '907': '确定删除命名空间：[ ',
  '908': '端点: ',
  '909': '端点 删除中...',
  '910': '端点 [ ',
  '911': '确定删除端点：[ ',
  '912': '路由: ',
  '913': '路由 删除中...',
  '914': '路由 [ ',
  '915': '确定删除路由：[ ',
  '916': '集群IP',
  '917': '服务: ',
  '918': '服务 删除中...',
  '919': '服务 [ ',
  '920': '确定删除服务：[ ',
  '921': '[主机详情]',
  '922': '主机YAML',
  '923': '恢复调度',
  '924': '封锁调度',
  '925': '排空节点',
  '926': '主机概览',
  '927': '主机状态',
  '928': '节点已封锁调度！',
  '929': '确定 封锁该节点调度 ?',
  '930': '节点已恢复调度！',
  '931': '确定 恢复该节点调度 ?',
  '932': '排空节点成功！',
  '933': '确定要排空该节点的所有容器吗？',
  '934': '主机YAML: ',
  '935': '大小',
  '936': '基本信息 ',
  '937': '注解/标签',
  '938': '注解:',
  '939': '可分配 ',
  '940': ' / 总量 ',
  '941': ' / 总量  ',
  '942': '容器组CIDR',
  '943': '系统体系架构',
  '944': '操作系统镜像',
  '945': '操作系统内核',
  '946': '内核容器引擎',
  '947': '容器',
  '948': ' 启动时间: ',
  '949': ' 容器镜像: ',
  '950': ' 退出编码: ',
  '951': '请输入主机名或IP',
  '952': '主机名',
  '953': '暂停调度',
  '954': '内网IP',
  '955': 'Kubelet版本',
  '956': 'OS版本',
  '957': '恢复调度 ',
  '958': '确定 封锁下面节点调度 ?',
  '959': '节点：',
  '960': '确定 恢复下面节点调度 ?',
  '961': '确定要排空下面节点的所有容器吗 ？',
  '962': '原因',
  '963': '最后心跳时间',
  '964': '最后变更时间',
  '965': '节点磁盘空间不够?',
  '966': '节点健康且就绪可以接受新的Pod?',
  '967': '节点内存紧张?',
  '968': '节点上进程过多?',
  '969': '节点磁盘空间紧张?',
  '970': '节点网络配置有问题?',
  '971': 'CPU 请求',
  '972': 'CPU 限制',
  '973': '内存 Gib',
  '974': '内存请求 Gib',
  '975': '内存限制 Gib',
  '976': 'Pod 数量',
  '977': '镜像仓库地址:',
  '978': '镜像仓库用户名:',
  '979': '计算资源',
  '980': 'Cron 表达式',
  '981': '是否挂起',
  '982': '上次调度时间',
  '983': '定时任务: ',
  '984': '工作负载-定时任务 删除中...',
  '985': '工作负载-定时任务 [ ',
  '986': '确定删除工作负载-定时任务：[ ',
  '987': '守护进程集: ',
  '988': '工作负载-守护进程集 [ ',
  '989': '确定删除工作负载-守护进程集：[ ',
  '990': ' 成功重启',
  '991': '确定 重启 工作负载-守护进程集：[ ',
  '992': '期望',
  '993': '当前',
  '994': '就绪',
  '995': '工作负载-部署 [ ',
  '996': '确定删除工作负载-部署：[ ',
  '997': '确定 重启 工作负载-部署：[ ',
  '998': '成功数 / 总数',
  '999': '开始时间',
  '1000': '完成时间',
  '1001': '任务: ',
  '1002': '工作负载-Job任务 删除中...',
  '1003': '工作负载-Job任务 [ ',
  '1004': '确定删除工作负载-Job任务：[ ',
  '1005': '状态统计',
  '1006': '事件[Events]：',
  '1007': '重启数',
  '1008': '工作负载-容器组 [ ',
  '1009': '确定删除工作负载-容器组：[ ',
  '1010': '有状态副本集: ',
  '1011': '工作负载-有状态副本集 [ ',
  '1012': '确定删除工作负载-有状态副本集：[ ',
  '1013': '确定 重启 工作负载-有状态副本集：[ ',
  '1014': '同时停止所有托管服务',
  '1015': '确认停止以下节点?',
  '1016': '请输入备份名称检索',
  '1017': '手工创建备份',
  '1018': '历史配置（长度）',
  '1019': '当前配置（长度）',
  '1020': '对比差异',
  '1021': '集群级属性配置',
  '1022': '集群级日志配置',
  '1023': '确定要还原 ',
  '1024': '] 吗？',
  '1025': '还原成功',
  '1026': '创建备份成功',
  '1027': '确定要删除备份 ',
  '1028': ' 吗？',
  '1029': '删除成功',
  '1030': '请输入配置名称检索',
  '1031': '新建配置',
  '1032': '导入配置',
  '1033': '导出配置',
  '1034': '删除选中配置',
  '1035': '名称:',
  '1036': '例如：数据库',
  '1037': '编码:',
  '1038': '例如: mysql',
  '1039': '内容:',
  '1040': '请选择配置名称',
  '1041': '请填写配置编码',
  '1042': '[配置名称]',
  '1043': '导出成功',
  '1044': '新建配置名称',
  '1045': '#注释\nkey=value',
  '1046': '确定要删除 选中的 配置吗？',
  '1047': '确定要删除 ',
  '1048': '] 配置吗？',
  '1049': '回到首页',
  '1050': '页面未找到!',
  '1051': '数据获取失败 ',
  '1052': '清空',
  '1053': '下载 ',
  '1054': '到顶部',
  '1055': '到底部',
  '1056': '暂停跟踪',
  '1057': '日志文件已经删除.',
  '1058': '删除日志出错',
  '1059': '确定删除服务日志吗?',
  '1060': '继续跟踪',
  '1061': '您的浏览器不支持websocket',
  '1062': ' 连接异常: %o',
  '1063': '用户名',
  '1064': '密码',
  '1065': '修改密码',
  '1066': '登 录',
  '1067': '请输入用户名',
  '1068': '请输入密码',
  '1069': '登录失败: ',
  '1070': '请求异常: HTTP ',
  '1071': '网络异常',
  '1072': '输入用户名',
  '1073': '原密码',
  '1074': '输入原密码',
  '1075': '新密码',
  '1076': '输入新密码',
  '1077': '确认新密码',
  '1078': '再次输入新密码以确认',
  '1079': '确认修改',
  '1080': '密码必须为8位以上包含大小写字母、数字、特殊字符',
  '1081': '两次输入密码不一致',
  '1082': '用户名不能为空',
  '1083': '请输入原密码',
  '1084': '请输入新密码',
  '1085': '请再次输入新密码以确认',
  '1086': '密码修改失败: ',
  '1087': '密码修改失败',
  '1088': '服务未启动',
  '1089': '获取Grafana地址失败',
  '1090': 'Skynet监控/仪表盘',
  '1091': ' 登录 ',
  '1092': '最新动态',
  '1093': '产品介绍',
  '1094': '用户案例',
  '1095': '联系我们',
  '1096': '个人中心',
  '1097': '跳转1',
  '1098': '跳转2',
  '1099': '未开启WebShell功能',
  '1100': '控制面板',
  '1101': '主机地址：',
  '1102': '资源账户：',
  '1103': '根路径',
  '1104': '请输入文件夹名称',
  '1105': '目录',
  '1106': '新建文件夹',
  '1107': '确认删除？',
  '1108': '文件夹名称不能为空！',
  '1109': '修改成功！',
  '1110': '未开启WebSFTP功能',
  '1111': '登录',
  '1112': '请输入IP地址',
  '1113': '连接',
  '1114': '请输入主机IP',
  '1115': '请输入主机端口',
  '1116': '向上一级',
  '1117': '请输入待编码或解码内容',
  '1118': 'Base64编码',
  '1119': 'Base64解码',
  '1120': '复制输出',
  '1121': '请输入待格式化内容',
  '1122': 'JSON格式化',
  '1123': '字符串格式错误',
  '1124': '请输入待加密或待解密内容',
  '1125': 'MD5加密',
  '1126': 'SkynetSecurity加密',
  '1127': 'SkynetSecurity解密',
  '1128': '密码加密解密',
  '1129': 'Base64编解码',
  '1130': 'URL编解码',
  '1131': 'MD5编解码',
  '1132': 'URI编码',
  '1133': 'URI解码',
  '1134': '关闭',
  '1135': '操作',
  '1201': 'Loki日志',
  '1202': '请选择App',
  '1203': '请选择Pod',
  '1204': '请选择时间',
  '1205': '请选择查询方向',
  '1206': '请选择查询日志条目',
  '1207': '查询',
  '1208': '之后',
  '1209': '之前',
  '1210': '条',
  '1211':'按F1显示全屏，按Esc退出全屏',
  '1212':'删除多余文件',
  '1213':'实例数：',
  '1214':'边车配置',
  '1215':'启用边车模式'
}
export default zhLocale

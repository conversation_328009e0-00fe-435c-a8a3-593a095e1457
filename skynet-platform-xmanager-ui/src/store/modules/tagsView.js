import locale from '@/i18n/index.js'
import router from '@/router'
const state = {
  visitedViews: [],
  cachedViews: [],
  cachedIframes: []
  // visitingIframe: ''
}
const mutations = {
  ADD_VISITED_VIEW: (state, { to, from }) => {
    if (state.visitedViews.some(v => v.path === to.path)) return
    let fromIdx = state.visitedViews.length
    if (from && from.path) {
      // 新tag的位置位于当前tag之后
      let indexFound = state.visitedViews.findIndex(v => {
        return v.path === from.path
      })
      if (indexFound > -1) {
        fromIdx = indexFound + 1
      }
    }
    let dynamicTitle = 'titleFunc' in to.meta ? to.meta.titleFunc(to) : to.meta.title // 动态标题，支持根据函数生成tag title
    let viewToAdd = Object.assign({}, to, {
      title: dynamicTitle || 'no-name'
    })
    state.visitedViews.splice(fromIdx, 0, viewToAdd)
    if (to.meta.noFocusOn) {
      router.push(from.path)
    }
  },
  ADD_CACHED_VIEW: (state, view) => {
    if (state.cachedViews.includes(view.name)) return
    if (!view.meta.noCache) {
      state.cachedViews.push(view.name)
    }
  },
  DEL_VISITED_VIEW: (state, view) => {
    for (const [i, v] of state.visitedViews.entries()) {
      if (v.path === view.path) {
        state.visitedViews.splice(i, 1)
        break
      }
    }
  },
  DEL_CACHED_VIEW: (state, view) => {
    // 处理多个标签页复用同一个组件的情况，仅当该组件对应的所有标签页都关闭时才删除
    let countInVisitedViews = state.visitedViews.filter(v => {
      return v.name === view.name
    }).length
    if (countInVisitedViews === 0) {
      const index = state.cachedViews.indexOf(view.name)
      index > -1 && state.cachedViews.splice(index, 1)
    }
  },
  FORCE_DEL_CACHED_VIEW: (state, view) => {
    const index = state.cachedViews.indexOf(view.name)
    index > -1 && state.cachedViews.splice(index, 1)
  },
  DEL_OTHERS_VISITED_VIEWS: (state, view) => {
    state.visitedViews = state.visitedViews.filter(v => {
      return v.meta.affix || v.path === view.path
    })
  },
  DEL_OTHERS_CACHED_VIEWS: (state, view) => {
    const index = state.cachedViews.indexOf(view.name)
    if (index > -1) {
      state.cachedViews = state.cachedViews.slice(index, index + 1)
    } else {
      // if index = -1, there is no cached tags
      state.cachedViews = []
    }
  },
  DEL_ALL_VISITED_VIEWS: state => {
    // keep affix tags
    const affixTags = state.visitedViews.filter(tag => tag.meta.affix)
    state.visitedViews = affixTags
  },
  DEL_ALL_CACHED_VIEWS: state => {
    state.cachedViews = []
  },
  UPDATE_VISITED_VIEW: (state, view) => {
    for (let v of state.visitedViews) {
      if (v.path === view.path) {
        v = Object.assign(v, view)
        break
      }
    }
  },
  UPDATE_VISITED_VIEWS: (state, views) => {
    state.visitedViews = views
  }
}
const actions = {
  addView({ dispatch }, { to, from }) {
    dispatch('addVisitedView', {
      to,
      from
    })
    dispatch('addCachedView', to)
  },
  addVisitedView({ commit }, { to, from }) {
    commit('ADD_VISITED_VIEW', {
      to,
      from
    })
  },
  addCachedView({ commit }, view) {
    commit('ADD_CACHED_VIEW', view)
  },
  delView({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delVisitedView', view)
      dispatch('delCachedView', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delVisitedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_VISITED_VIEW', view)
      resolve([...state.visitedViews])
    })
  },
  delCachedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_CACHED_VIEW', view)
      resolve([...state.cachedViews])
    })
  },
  forceDelCachedView({ commit, state }, view) {
    return new Promise(resolve => {
      commit('FORCE_DEL_CACHED_VIEW', view)
      resolve([...state.cachedViews])
    })
  },
  delOthersViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delOthersVisitedViews', view)
      dispatch('delOthersCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delOthersVisitedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_VISITED_VIEWS', view)
      resolve([...state.visitedViews])
    })
  },
  delOthersCachedViews({ commit, state }, view) {
    return new Promise(resolve => {
      commit('DEL_OTHERS_CACHED_VIEWS', view)
      resolve([...state.cachedViews])
    })
  },
  delAllViews({ dispatch, state }, view) {
    return new Promise(resolve => {
      dispatch('delAllVisitedViews', view)
      dispatch('delAllCachedViews', view)
      resolve({
        visitedViews: [...state.visitedViews],
        cachedViews: [...state.cachedViews]
      })
    })
  },
  delAllVisitedViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_VISITED_VIEWS')
      resolve([...state.visitedViews])
    })
  },
  delAllCachedViews({ commit, state }) {
    return new Promise(resolve => {
      commit('DEL_ALL_CACHED_VIEWS')
      resolve([...state.cachedViews])
    })
  },
  updateVisitedView({ commit }, view) {
    commit('UPDATE_VISITED_VIEW', view)
  },
  updateVisitedViews({ commit }, views) {
    commit('UPDATE_VISITED_VIEWS', views)
  }
}
export default {
  namespaced: true,
  state,
  mutations,
  actions
}

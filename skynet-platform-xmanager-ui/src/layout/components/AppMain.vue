<template>
  <section class="app-main" id="appMain">
    <!-- <transition name="fade-transform" mode="out-in"> -->
    <keep-alive :include="cachedViews">
      <router-view :key="key" />
    </keep-alive>
    <!-- </transition> -->
    <iframe v-for="(obj, index) in cachedIframes" :key="index" :src="obj.url" v-show="obj.url === visitingIframe" frameborder="0"></iframe>
  </section>
</template>
<script>
import locale from '@/i18n/index.js'

import { mapGetters } from 'vuex'
export default {
  name: 'AppMain',
  computed: {
    cachedViews() {
      return this.$store.state.tagsView.cachedViews
    },
    key() {
      return this.$route.path
    },
    ...mapGetters(['cachedIframes', 'visitingIframe'])
  },
  created() {}
}
</script>
<style scoped lang="scss">
.app-main {
  /* 50= navbar  50  */
  // min-height: calc(100vh - 100px);
  min-height: 100%;
  width: 100%;
  position: relative;
  overflow: hidden;
  background: #f5f6fa;
  padding: 15px;

  & iframe {
    height: 100%;
    width: 100%;
    background-color: #ffffff;
  }
}

.fixed-header + .app-main {
  padding-top: $appMainNoTagTopPadding;
}
.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    height: calc(100vh - #{$headtopHeight});
  }

  .fixed-header + .app-main {
    padding-top: $appMainTopPadding;
  }
}
</style>
<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>

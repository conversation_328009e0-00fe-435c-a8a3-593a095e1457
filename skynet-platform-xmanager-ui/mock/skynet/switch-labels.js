export default 	[
  { 'title': '启用监控采集[Prometheus]', 'code': 'enablePrometheusTarget', 'extPlaceHolder': '例如 /metrics:15s', 'extTitle': 'Prometheus采集点配置', 'boots': [] },
  { 'title': '启用Ant追踪[Skywalking]', 'code': 'enableSkywalking', 'extPlaceHolder': '', 'extTitle': '', 'boots': ['SpringBoot', 'SkynetBoot'] },
  { 'title': '启用Nginx网关', 'code': 'enableNginxGateway', 'extPlaceHolder': '请输入服务别名', 'extTitle': '服务别名', 'boots': [] },
  { 'title': '参与流程计算(分布式计算平台)', 'code': 'enableStream', 'extPlaceHolder': '', 'extTitle': '', 'boots': ['SpringBoot', 'SkynetBoot'] }
]

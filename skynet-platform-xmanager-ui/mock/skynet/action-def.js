let mockActionDefinitions = [
  {
    'actionCode': 'actionPoint1',
    'pluginCode': 'demoPlugin1',
    'actionPoint': 'actionPoint1@demoPlugin1',
    'actionName': 'DEMO服务1',
    'pluginName': 'demo系统1',
    'description': 'string',
    'index': 0,
    'type': 'SpringBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9
    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['AAA'],
    'properties': 'string',
    'loggingLevels': 'string'
  },

  {
    'actionCode': 'actionPoint2',
    'pluginCode': 'demoPlugin1',
    'actionPoint': 'actionPoint2@demoPlugin1',
    'actionName': 'DEMO服务2',
    'pluginName': 'demo系统1',
    'description': 'string',
    'index': 0,
    'type': 'SpringBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9
    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['AAA', 'BBB'],
    'properties': 'string',
    'loggingLevels': 'string'
  },

  {
    'actionCode': 'actionPoint3',
    'pluginCode': 'demoPlugin2',
    'actionPoint': 'actionPoint3@demoPlugin2',
    'actionName': 'DEMO服务3',
    'pluginName': 'demo系统2',
    'description': 'string',
    'index': 0,
    'type': 'BaseBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9

    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['DEMO'],
    'properties': 'string',
    'loggingLevels': 'string'
  },

  {
    'actionCode': 'actionPoint4',
    'pluginCode': 'demoPlugin2',
    'actionPoint': 'actionPoint4@demoPlugin2',
    'actionName': 'DEMO服务4',
    'pluginName': 'demo系统2',
    'description': 'string',
    'index': 0,
    'type': 'BaseBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9

    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['AAA'],
    'properties': 'string',
    'loggingLevels': 'string'
  },

  {
    'actionCode': 'actionPoint5',
    'pluginCode': 'demoPlugin3',
    'actionPoint': 'actionPoint5@demoPlugin3',
    'actionName': 'DEMO服务5',
    'pluginName': 'demo系统3',
    'description': 'string',
    'index': 0,
    'type': 'DockerBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9
    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['DEMO'],
    'properties': 'string',
    'loggingLevels': 'string'
  },

  {
    'actionCode': 'actionPoint6',
    'pluginCode': 'demoPlugin3',
    'actionPoint': 'actionPoint6@demoPlugin3',
    'actionName': 'DEMO服务6',
    'pluginName': 'demo系统3',
    'description': 'string',
    'index': 0,
    'type': 'SkynetBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9
    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['DEMO'],
    'properties': 'string',
    'loggingLevels': 'string'
  },

  {
    'actionCode': 'actionPoint7',
    'pluginCode': 'demoPlugin4',
    'actionPoint': 'actionPoint7@demoPlugin4',
    'actionName': 'DEMO服务7',
    'pluginName': 'demo系统4',
    'description': 'string',
    'index': 0,
    'type': 'SkynetBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9
    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['AAA', 'BBB'],
    'properties': 'string',
    'loggingLevels': 'string'
  },

  {
    'actionCode': 'mysql',
    'actionName': 'mysql',
    'actionPoint': 'mysql@paas',
    'pluginCode': 'paas',
    'pluginName': '基础服务',
    'description': 'string',
    'index': 0,
    'type': 'BaseBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9
    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'properties': 'string',
    'loggingLevels': 'string'
  },
  {
    'actionCode': 'es',
    'actionName': 'Elasticsearch',
    'actionPoint': 'es@paas',
    'pluginCode': 'paas',
    'pluginName': '基础服务',
    'description': 'string',
    'index': 0,
    'type': 'BaseBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9
    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],

    'properties': 'string',
    'loggingLevels': 'string'
  },
  {
    'actionCode': 'api',
    'actionName': '人脸服务-api',
    'actionPoint': 'api@face',
    'pluginCode': 'face',
    'pluginName': '人脸识别服务',
    'description': 'string',
    'index': 0,
    'type': 'SpringBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9,
      'tags': ['AAA', 'CCC']
    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['CCC'],
    'properties': 'string',
    'loggingLevels': 'string'
  },
  {
    'actionCode': 'engine',
    'actionName': '人脸服务-引擎',
    'actionPoint': 'engine@face',
    'pluginCode': 'face',
    'pluginName': '人脸识别服务',
    'description': 'string',
    'index': 0,
    'type': 'BaseBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9
    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['AAA', 'DEMO'],
    'properties': 'string',
    'loggingLevels': 'string'
  },
  {
    'actionCode': 'prometheus',
    'actionName': 'prometheus',
    'actionPoint': 'prometheus@ant-mon',
    'pluginCode': 'ant-mon',
    'pluginName': 'skynet-监控',
    'description': 'string',
    'index': 0,
    'type': 'BaseBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9
    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['DEMO'],
    'properties': 'string',
    'loggingLevels': 'string'
  },
  {
    'actionCode': 'grafana',
    'actionName': 'grafana',
    'actionPoint': 'grafana@ant-mon',
    'pluginCode': 'ant-mon',
    'pluginName': 'skynet-监控',
    'description': 'string',
    'index': 0,
    'type': 'DockerBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9
    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['AAA', 'DDD', 'DEMO'],
    'properties': 'string',
    'loggingLevels': 'string'
  },
  {
    'actionCode': 'node-exporter',
    'actionName': '主机监控',
    'actionPoint': 'node-exporter@ant-mon',
    'pluginCode': 'ant-mon',
    'pluginName': 'skynet-监控',
    'description': 'string',
    'index': 0,
    'type': 'BaseBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9

    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['DEMO'],
    'properties': 'string',
    'loggingLevels': 'string'
  },
  {
    'actionCode': 'config-center',
    'actionName': '监控配置中心',
    'actionPoint': 'config-center@ant-mon',
    'pluginCode': 'ant-mon',
    'pluginName': 'skynet-监控',
    'description': 'string',
    'index': 0,
    'type': 'SpringBoot',
    'protocol': 'HTTP',
    'port': 2222,
    'homePageURL': 'string',
    'referencedFiles': [
      {
        'fileName': 'file1',
        'targetDir': '/xxx/yyy/zzz'
      },
      {
        'fileName': 'file2',
        'targetDir': '/xxx/yyy/zzz'
      }
    ],
    'startupConfig': {
      'workingDir': '/home/<USER>',
      'runnableJar': 'xxx-yyy-v2.1.1.jar',
      'cmd': './start.sh',
      'javaCmdOptions': '-Xms=512m -Xmx=1g',
      'skynetRunParam': '',
      'programArguments': '--my-config=xxx',
      'sysEnvironments': {
        'ENV_DEMO1': 'string',
        'ENV_DEMO2': 'string',
        'ENV_DEMO3': 'string'
      },
      'dockerRunOptions': '-p 8090:8080 -v /path1:/path2',
      'dockerRunCmdAndArgs': './start.sh foo1 foo2',
      'dockerContainerName': 'edu_container',
      'dockerImage': 'edu_service',
      'signalToStop': -9

    },
    'healthCheckConfig': {
      'type': 'protocol',
      'url': '/index',
      'delaySeconds': 10,
      'intervalSeconds': 20,
      'timeoutSeconds': 30,
      'retryTimes': 3
    },
    'integrationConfig': {
      'logbackLogCollection': true
    },
    'switchLabels': [
      {
        'code': 'enablePrometheusTarget',
        'extProperty': '/metrics:15s',
        'title': '启用监控采集',
        'value': true
      }, {
        'code': 'enableNginxGateway',
        'extProperty': 'swk',
        'title': '启用Nginx网关',
        'value': true
      }
    ],
    'tags': ['DDD'],
    'properties': 'string',
    'loggingLevels': 'string'
  }
]

export default mockActionDefinitions

let mockDeployment =
[
  {
    'actions': [
      {
        'actionID': 'mysql@paas',
        'actionName': 'mysql',
        'actionPoint': 'mysql@paas',
        'pluginCode': 'paas',
        'pluginName': '基础服务',
        'enabled': true,
        'homePageURL': '',
        'instanceIndex': 0,
        'pid': 3332,
        'port': 2630,
        'startTime': '2020-07-04 00:00:00',
        'startupOrder': 1,
        'status': 'UP',
        'upTime': '12小时12分'
      },
      {
        'actionID': 'es@paas',
        'actionName': 'Elasticsearch',
        'actionPoint': 'es@paas',
        'pluginCode': 'paas',
        'pluginName': '基础服务',
        'enabled': true,
        'homePageURL': '',
        'instanceIndex': 0,
        'pid': 3335,
        'port': 2631,
        'startTime': '2020-07-04 00:00:00',
        'startupOrder': 2,
        'status': 'UP',
        'upTime': '12小时12分'
      },
      {
        'actionID': 'api@face',
        'actionName': '人脸服务-api',
        'actionPoint': 'api@face',
        'pluginCode': 'face',
        'pluginName': '人脸识别服务',
        'enabled': true,
        'homePageURL': '',
        'instanceIndex': 0,
        'pid': 33312,
        'port': 28000,
        'startTime': '2020-07-04 00:00:00',
        'startupOrder': 2,
        'status': 'UP',
        'upTime': '12小时12分'
      },
      {
        'actionID': 'engine@face_0',
        'actionName': '人脸服务-引擎',
        'actionPoint': 'engine@face',
        'pluginCode': 'face',
        'pluginName': '人脸识别服务',
        'enabled': true,
        'homePageURL': '',
        'instanceIndex': 0,
        'pid': 33313,
        'port': 28001,
        'startTime': '2020-07-04 00:00:00',
        'startupOrder': 3,
        'status': 'UP',
        'upTime': '12小时12分'
      },
      {
        'actionID': 'engine@face_1',
        'actionName': '人脸服务-引擎',
        'actionPoint': 'engine@face',
        'pluginCode': 'face',
        'pluginName': '人脸识别服务',
        'enabled': true,
        'homePageURL': '',
        'instanceIndex': 1,
        'pid': 33315,
        'port': 28002,
        'startTime': '2020-07-04 00:00:00',
        'startupOrder': 3,
        'status': 'UP',
        'upTime': '12小时12分'
      }
    ],
    'agentActionID': 'ant-xagent@ant',
    'agentActionPoint': 'ant-xagent@ant',
    'agentPort': 6230,
    'agentStatus': 'UP',
    'ip': '***************',
    'serverInfo': {
      cpu: 72,
      mem: 256,
      gpu: 'Tesla P4*1'
    },
    'serverTags': ['人脸应用']
  },
  {
    'actions': [
      {
        'actionID': 'prometheus@ant-mon',
        'actionName': 'prometheus',
        'actionPoint': 'prometheus@ant-mon',
        'pluginCode': 'ant-mon',
        'pluginName': 'skynet-监控',
        'enabled': true,
        'homePageURL': '',
        'instanceIndex': 0,
        'pid': 3332,
        'port': 2630,
        'startTime': '2020-07-04 00:00:00',
        'startupOrder': 1,
        'status': 'UP',
        'upTime': '12小时12分'
      },
      {
        'actionID': 'grafana@ant-mon',
        'actionName': 'grafana',
        'actionPoint': 'grafana@ant-mon',
        'pluginCode': 'ant-mon',
        'pluginName': 'skynet-监控',
        'enabled': true,
        'homePageURL': '',
        'instanceIndex': 0,
        'pid': 3335,
        'port': 2631,
        'startTime': '2020-07-04 00:00:00',
        'startupOrder': 2,
        'status': 'DOWN',
        'upTime': '12小时12分'
      },
      {
        'actionID': 'node-exporter@ant-mon',
        'actionName': '主机监控',
        'actionPoint': 'node-exporter@ant-mon',
        'pluginCode': 'ant-mon',
        'pluginName': 'skynet-监控',
        'enabled': false,
        'homePageURL': '',
        'instanceIndex': 0,
        'pid': 33312,
        'port': 28000,
        'startTime': '2020-07-04 00:00:00',
        'startupOrder': 4,
        'status': 'LOADING',
        'upTime': '12小时12分'
      },
      {
        'actionID': 'config-center@ant-mon',
        'actionName': '监控配置中心',
        'actionPoint': 'config-center@ant-mon',
        'pluginCode': 'ant-mon',
        'pluginName': 'skynet-监控',
        'enabled': true,
        'homePageURL': '',
        'instanceIndex': 0,
        'pid': 33313,
        'port': 28001,
        'startTime': '2020-07-04 00:00:00',
        'startupOrder': 4,
        'status': 'UP',
        'upTime': '12小时12分'
      }
    ],
    'agentActionID': 'ant-xagent@ant',
    'agentActionPoint': 'ant-xagent@ant',
    'agentPort': 6230,
    'agentStatus': 'UP',
    'ip': '***************',
    'serverInfo': {
      cpu: 72,
      mem: 256,
      gpu: 'Tesla T4*1'
    },
    'serverTags': ['监控基础层']
  },
  {
    'ip': '***************',
    'agentStatus': 'UP',
    'serverTags': ['监控基础层', '人脸应用'],
    'actions': [
      {
        'actionID': 'engine@face',
        'actionName': '人脸服务-引擎',
        'actionPoint': 'engine@face',
        'pluginCode': 'face',
        'pluginName': '人脸识别服务',
        'enabled': true,
        'homePageURL': '',
        'instanceIndex': 0,
        'pid': 33344,
        'port': 28001,
        'startTime': '2020-07-04 00:00:00',
        'startupOrder': 3,
        'status': 'UP',
        'upTime': '12小时12分'
      }
    ]
  },
  {
    'ip': '***************',
    'agentStatus': 'UP'
  }
]

export default mockDeployment
